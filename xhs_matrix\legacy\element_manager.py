#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
元素管理器模块
负责元素定位和交互
"""

import os
import sys
import json
import time
import traceback
import random
from typing import Dict, List, Optional, Any, Tuple, Union
from pathlib import Path

# 新系统独立导入
from xhs_matrix.common.logger import logger

# 新系统的设备类型
from xhs_matrix.core.enhanced_rpc_client import OptimizedMytRpc as XHSDevice


class ElementLocator:
    """元素定位器，负责查找元素"""

    def __init__(self):
        """初始化元素定位器"""
        # 初始化图像定位器
        try:
            from xhs_matrix.legacy.image_element_locator import ImageElementLocator
            # 设置正确的模板目录
            self.image_locator = ImageElementLocator(templates_dir="configs")
            logger.info("ElementLocator: 图像定位器初始化成功")
        except ImportError as e:
            self.image_locator = None
            logger.warning(f"ElementLocator: 图像定位器不可用: {e}")

    def locate_by_strategy(self, device: XHSDevice, strategy: Dict, node_config: Dict) -> Any:
        """
        根据策略定位元素
        
        Args:
            device: 设备对象
            strategy: 定位策略
            node_config: 节点配置
            
        Returns:
            定位结果（节点对象或坐标）
        """
        strategy_type = strategy.get("type", "")
        
        try:
            if strategy_type == "coordinates" or strategy_type == "position":
                # 坐标定位
                x = strategy.get("x") or node_config.get("fallback", [0, 0])[0]
                y = strategy.get("y") or node_config.get("fallback", [0, 0])[1]
                return {"x": x, "y": y, "type": "coordinates"}
                
            elif strategy_type == "text":
                # 文本定位
                text = strategy.get("text") or node_config.get("text", "")
                if not text:
                    return None
                    
                selector = device.create_selector()
                if not selector:
                    return None
                    
                selector.addQuery_TextEqual(text)
                node = selector.execQueryOne(2000)
                return node
                
            elif strategy_type == "resource-id":
                # ID定位
                resource_id = strategy.get("resource-id") or node_config.get("resource-id", "")
                if not resource_id:
                    return None
                    
                selector = device.create_selector()
                if not selector:
                    return None
                    
                selector.addQuery_IdEqual(resource_id)
                node = selector.execQueryOne(2000)
                return node
                
            elif strategy_type == "class":
                # 类名定位
                class_name = strategy.get("class") or node_config.get("class", "")
                text = strategy.get("text") or node_config.get("text", "")
                if not class_name:
                    return None
                    
                selector = device.create_selector()
                if not selector:
                    return None
                    
                selector.addQuery_ClzEqual(class_name)
                if text:
                    selector.addQuery_TextEqual(text)
                node = selector.execQueryOne(2000)
                return node
                
            elif strategy_type == "attributes":
                # 属性组合定位
                selector = device.create_selector()
                if not selector:
                    return None
                    
                # 添加所有可用的属性条件
                if strategy.get("resource-id") or node_config.get("resource-id"):
                    selector.addQuery_IdEqual(strategy.get("resource-id") or node_config.get("resource-id"))
                    
                if strategy.get("class") or node_config.get("class"):
                    selector.addQuery_ClzEqual(strategy.get("class") or node_config.get("class"))
                    
                if strategy.get("text") or node_config.get("text"):
                    selector.addQuery_TextEqual(strategy.get("text") or node_config.get("text"))
                    
                if strategy.get("content-desc") or node_config.get("content-desc"):
                    selector.addQuery_DescEqual(strategy.get("content-desc") or node_config.get("content-desc"))
                
                # 获取符合条件的所有元素
                nodes = selector.execQuery(10, 2000)
                if not nodes or len(nodes) == 0:
                    return None
                    
                # 如果只有一个元素，直接返回
                if len(nodes) == 1:
                    return nodes[0]
                    
                # 如果有多个元素，需要进一步筛选
                logger.debug(f"找到多个匹配元素({len(nodes)}个)，尝试筛选")
                
                # 获取额外的筛选条件
                index = strategy.get("index", node_config.get("index", 0))
                contains_text = strategy.get("contains_text", node_config.get("contains_text", ""))
                visible_only = strategy.get("visible_only", node_config.get("visible_only", True))
                
                # 筛选可见元素
                if visible_only:
                    visible_nodes = []
                    for n in nodes:
                        try:
                            if hasattr(n, "getVisible") and n.getVisible():
                                visible_nodes.append(n)
                        except:
                            pass
                            
                    if visible_nodes:
                        nodes = visible_nodes
                        
                # 筛选包含特定文本的元素
                if contains_text:
                    text_nodes = []
                    for n in nodes:
                        try:
                            if hasattr(n, "getText") and contains_text in n.getText():
                                text_nodes.append(n)
                        except:
                            pass
                            
                    if text_nodes:
                        nodes = text_nodes
                
                # 如果经过筛选后还有多个元素，使用索引
                if nodes and 0 <= index < len(nodes):
                    return nodes[index]
                    
                # 无法确定唯一元素，返回第一个
                if nodes:
                    return nodes[0]
                    
                return None
                
            elif strategy_type == "ui_path":
                # UI路径定位
                path = strategy.get("path", [])
                if not path:
                    return None
                    
                # 从根节点开始查找
                current_node = None
                selector = device.create_selector()
                
                for i, step in enumerate(path):
                    step_selector = current_node.create_selector() if current_node else device.create_selector()
                    if not step_selector:
                        return None
                        
                    # 添加当前步骤的所有查询条件
                    if step.get("class"):
                        step_selector.addQuery_ClzEqual(step["class"])
                    if step.get("resource-id"):
                        step_selector.addQuery_IdEqual(step["resource-id"])
                    if step.get("text"):
                        step_selector.addQuery_TextEqual(step["text"])
                    if step.get("content-desc"):
                        step_selector.addQuery_DescEqual(step["content-desc"])
                        
                    # 查找符合条件的节点
                    if i < len(path) - 1:
                        # 中间节点，查找单个节点
                        current_node = step_selector.execQueryOne(2000)
                        if not current_node:
                            return None
                    else:
                        # 最后一个步骤，可能有多个节点需要进一步筛选
                        nodes = step_selector.execQuery(10, 2000)
                        if not nodes or len(nodes) == 0:
                            return None
                            
                        # 使用索引选择节点
                        index = step.get("index", 0)
                        if 0 <= index < len(nodes):
                            return nodes[index]
                        return nodes[0]
                        
                return None
                
            elif strategy_type == "ui_feature":
                # UI特征定位
                ui_type = strategy.get("ui_type") or node_config.get("ui_type")
                if not ui_type:
                    return None
                    
                # 实现UI特征定位（这里只提供一个简化的示例）
                if ui_type == "like_button":
                    # 查找点赞按钮
                    try:
                        # 1. 先找到笔记容器
                        post_container = device.create_selector()
                        if not post_container:
                            return None
                            
                        post_container.addQuery_ClzEqual("android.widget.FrameLayout")
                        post_container_node = post_container.execQueryOne(2000)
                        if not post_container_node:
                            return None
                            
                        # 2. 找到操作栏
                        action_bar_selector = device.create_selector()  # 使用设备级别的选择器
                        if not action_bar_selector:
                            return None
                            
                        action_bar_selector.addQuery_ClzEqual("android.widget.LinearLayout")
                        action_bar_node = action_bar_selector.execQueryOne(2000)
                        if not action_bar_node:
                            return None
                            
                        # 3. 查找按钮
                        buttons_selector = device.create_selector()  # 使用设备级别的选择器
                        if not buttons_selector:
                            return None
                            
                        buttons_selector.addQuery_ClzEqual("android.widget.ImageView")
                        button_nodes = buttons_selector.execQuery(10, 2000)
                        if not button_nodes or len(button_nodes) == 0:
                            return None
                            
                        # 4. 点赞按钮通常是第一个按钮
                        return button_nodes[0]
                    except Exception as e:
                        logger.error(f"UI特征定位异常: {str(e)}")
                        return None
                        
                return None

            elif strategy_type == "image_template":
                # 图像模板定位
                if not self.image_locator:
                    logger.error("图像定位器不可用，无法使用image_template策略")
                    return None

                try:
                    # 获取模板路径
                    template_path = node_config.get("template_path", "")
                    if not template_path:
                        logger.error("image_template策略缺少template_path配置")
                        return None

                    # 获取阈值
                    threshold = node_config.get("threshold", 0.6)

                    # 获取当前屏幕截图
                    screenshot = device.take_capture_compress()
                    if not screenshot:
                        logger.error("无法获取屏幕截图")
                        return None

                    # 将截图数据转换为图像
                    import numpy as np
                    import cv2

                    # 将字节数据转换为numpy数组
                    nparr = np.frombuffer(screenshot, np.uint8)
                    # 解码图像
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    if image is None:
                        logger.error("无法解码屏幕截图")
                        return None

                    # 使用图像定位器查找元素
                    result = self.image_locator.locate_element_by_template(
                        image=image,
                        template_path=template_path,
                        threshold=threshold
                    )

                    if result:
                        # 转换为坐标格式
                        center_x = result.get("center", [0, 0])[0]
                        center_y = result.get("center", [0, 0])[1]
                        logger.info(f"图像模板定位成功: {template_path} at ({center_x}, {center_y})")
                        return {
                            "x": center_x,
                            "y": center_y,
                            "type": "coordinates",
                            "bounds": result.get("position", [])
                        }
                    else:
                        logger.warning(f"图像模板定位失败: {template_path}")
                        return None

                except Exception as e:
                    logger.error(f"图像模板定位异常: {str(e)}")
                    return None

            else:
                logger.warning(f"未知的定位策略类型: {strategy_type}")
                return None
                
        except Exception as e:
            logger.error(f"定位过程异常: {str(e)}")
            return None


class ElementInteractor:
    """元素交互器，负责执行操作"""

    def click(self, device: XHSDevice, element_result: Any) -> bool:
        """
        点击元素
        
        Args:
            device: 设备对象
            element_result: 定位结果
            
        Returns:
            是否点击成功
        """
        if not element_result:
            return False
            
        try:
            # 处理坐标点击
            if isinstance(element_result, dict) and element_result.get("type") == "coordinates":
                # 确保坐标是整数类型（处理numpy类型）
                x = int(element_result.get("x", 0))
                y = int(element_result.get("y", 0))

                # 添加随机偏移，在原坐标附近产生随机点击位置
                # 针对小红书按钮特点优化：横向偏移±10像素，纵向偏移±5像素
                offset_x = random.randint(-10, 10)
                offset_y = random.randint(-5, 5)

                # 应用偏移
                final_x = x + offset_x
                final_y = y + offset_y

                logger.info(f"通过坐标点击(含随机偏移): 原始({x}, {y}) -> 实际({final_x}, {final_y})")
                return device.tap(final_x, final_y)
                
            # 处理节点点击
            if hasattr(element_result, "Click_events") and callable(element_result.Click_events): # type: ignore
                logger.info("通过节点点击")
                return element_result.Click_events() # type: ignore
                
            # 处理节点坐标点击
            if hasattr(element_result, "getBounds") and callable(element_result.getBounds): # type: ignore
                try:
                    bounds = element_result.getBounds() # type: ignore
                    if bounds and len(bounds) == 4:
                        left, top, right, bottom = bounds
                        
                        # 计算中心点
                        center_x = (left + right) // 2
                        center_y = (top + bottom) // 2
                        
                        # 添加随机偏移，但确保在节点边界内
                        # 针对小红书按钮特点优化偏移范围：横向偏移更大，纵向偏移更小
                        max_offset_x = min(15, (right - left) // 4)
                        max_offset_y = min(8, (bottom - top) // 4)  # 减小纵向偏移
                        
                        # 在允许范围内随机偏移
                        offset_x = random.randint(-max_offset_x, max_offset_x)
                        offset_y = random.randint(-max_offset_y, max_offset_y)
                        
                        # 应用偏移
                        x = center_x + offset_x
                        y = center_y + offset_y
                        
                        logger.info(f"通过节点边界点击(含随机偏移): 中心点({center_x}, {center_y}) -> 实际({x}, {y})")
                        return device.tap(x, y)
                except Exception as e:
                    logger.error(f"获取节点边界异常: {str(e)}")
                    
            logger.warning("无法对元素执行点击操作")
            return False
            
        except Exception as e:
            logger.error(f"点击操作异常: {str(e)}")
            return False
    
    def long_click(self, device: XHSDevice, element_result: Any) -> bool:
        """长按元素"""
        if not element_result:
            return False
            
        try:
            # 处理坐标长按
            if isinstance(element_result, dict) and element_result.get("type") == "coordinates":
                x = element_result.get("x", 0)
                y = element_result.get("y", 0)
                logger.info(f"通过坐标长按: ({x}, {y})")
                if hasattr(device, 'myt') and device.myt is not None and hasattr(device.myt, 'longClick'):
                    return device.myt.longClick(1, x, y, 1.0)
                # 尝试使用直接长按
                elif hasattr(device, 'touchDown') and hasattr(device, 'touchUp'):
                    try:
                        device.touchDown(1, x, y)
                        time.sleep(1.0)  # 长按1秒
                        device.touchUp(1, x, y)
                        return True
                    except Exception as e:
                        logger.error(f"模拟长按异常: {str(e)}")
                        return False
                else:
                    logger.error("设备不支持长按操作")
                    return False
                
            # 处理节点长按
            if hasattr(element_result, "longClick_events") and callable(element_result.longClick_events): # type: ignore
                logger.info("通过节点长按")
                return element_result.longClick_events() # type: ignore
                
            # 处理节点坐标长按
            if hasattr(element_result, "getBounds") and callable(element_result.getBounds): # type: ignore
                try:
                    bounds = element_result.getBounds() # type: ignore
                    if bounds and len(bounds) == 4:
                        left, top, right, bottom = bounds
                        x = (left + right) // 2
                        y = (top + bottom) // 2
                        logger.info(f"通过节点边界长按: ({x}, {y})")
                        if hasattr(device, 'myt') and device.myt is not None and hasattr(device.myt, 'longClick'):
                            return device.myt.longClick(1, x, y, 1.0)
                        # 尝试使用直接长按
                        elif hasattr(device, 'touchDown') and hasattr(device, 'touchUp'):
                            try:
                                device.touchDown(1, x, y)
                                time.sleep(1.0)  # 长按1秒
                                device.touchUp(1, x, y)
                                return True
                            except Exception as e:
                                logger.error(f"模拟长按异常: {str(e)}")
                                return False
                        else:
                            logger.error("设备不支持长按操作")
                            return False
                except Exception as e:
                    logger.error(f"获取节点边界异常: {str(e)}")
                    
            logger.warning("无法对元素执行长按操作")
            return False
            
        except Exception as e:
            logger.error(f"长按操作异常: {str(e)}")
            return False


class ElementManager:
    """元素管理器，统一管理元素定位和交互"""
    
    def __init__(self, device: XHSDevice):
        """
        初始化元素管理器

        Args:
            device: 设备对象
        """
        self.device = device
        self.locator = ElementLocator()
        self.interactor = ElementInteractor()
        self.config_cache = {}  # 缓存已加载的配置

        # 初始化图像定位器
        try:
            from xhs_matrix.legacy.image_element_locator import ImageElementLocator
            # 设置正确的模板目录
            self.image_locator = ImageElementLocator(templates_dir="configs")
            logger.info("图像定位器初始化成功")
        except ImportError as e:
            self.image_locator = None
            logger.warning(f"图像定位器不可用: {e}")
        
    def load_config(self, config_path: str) -> int:
        """
        加载元素配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            加载的元素数量
        """
        try:
            if not os.path.exists(config_path):
                logger.error(f"配置文件不存在: {config_path}")
                return 0
                
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 初始化元素缓存
            self.config_cache = {}
            
            # 处理全局元素
            if "elements" in config:
                for element_id, element_config in config.get("elements", {}).items():
                    # 验证配置
                    errors = self.validate_element_config(element_config)
                    if errors:
                        logger.warning(f"元素 '{element_id}' 配置错误: {', '.join(errors)}")
                    self.config_cache[element_id] = element_config
            
            # 处理各个屏幕中的元素
            if "screens" in config:
                for screen_name, screen_config in config.get("screens", {}).items():
                    if "elements" in screen_config:
                        for element_id, element_config in screen_config["elements"].items():
                            # 验证配置
                            errors = self.validate_element_config(element_config)
                            if errors:
                                logger.warning(f"屏幕 '{screen_name}' 中的元素 '{element_id}' 配置错误: {', '.join(errors)}")
                            self.config_cache[element_id] = element_config
            
            logger.info(f"已加载 {len(self.config_cache)} 个元素配置")
            return len(self.config_cache)
            
        except Exception as e:
            logger.error(f"加载配置异常: {str(e)}")
            return 0
    
    def validate_element_config(self, config: Dict) -> List[str]:
        """
        验证元素配置的正确性
        
        Args:
            config: 元素配置
            
        Returns:
            错误列表
        """
        errors = []
        
        # 检查必要字段
        if "name" not in config:
            errors.append("缺少元素名称")
        
        # 检查定位策略
        strategies = config.get("strategies", [])
        if not strategies:
            # 如果没有定义strategies，检查是否有基本定位属性
            has_basic_attrs = any([
                "text" in config,
                "resource-id" in config,
                "class" in config,
                "fallback" in config,
                "ui_type" in config
            ])
            
            if not has_basic_attrs:
                errors.append("没有定义任何定位策略或属性")
        
        # 检查每个策略的有效性
        for i, strategy in enumerate(strategies):
            if "type" not in strategy:
                errors.append(f"第 {i+1} 个策略缺少类型定义")
            elif strategy["type"] == "coordinates" and ("x" not in strategy or "y" not in strategy):
                errors.append(f"坐标策略缺少 x 或 y 值")
            elif strategy["type"] == "text" and "text" not in strategy:
                errors.append(f"文本策略缺少 text 值")
            elif strategy["type"] == "resource-id" and "resource-id" not in strategy:
                errors.append(f"ID策略缺少 resource-id 值")
            elif strategy["type"] == "class" and "class" not in strategy:
                errors.append(f"类名策略缺少 class 值")
            elif strategy["type"] == "ui_path" and "path" not in strategy:
                errors.append(f"UI路径策略缺少 path 值")
        
        return errors
    
    def find_element(self, element_id_or_config: Union[str, Dict]) -> Any:
        """
        查找元素
        
        Args:
            element_id_or_config: 元素ID或配置
            
        Returns:
            查找到的元素
        """
        try:
            # 处理元素ID
            if isinstance(element_id_or_config, str):
                element_id = element_id_or_config
                if element_id not in self.config_cache:
                    logger.error(f"元素 '{element_id}' 未在配置中定义")
                    return None
                
                config = self.config_cache[element_id]
            else:
                # 直接使用传入的配置
                config = element_id_or_config
            
            # 获取策略列表
            strategies = config.get("strategies", [])
            if not strategies:
                # 如果没有定义strategies，根据locate_by配置生成策略
                locate_by = config.get("locate_by", [])
                if locate_by:
                    # 根据locate_by生成策略列表
                    strategies = []
                    for strategy_type in locate_by:
                        if strategy_type == "image_template" and "template_path" in config:
                            strategies.append({
                                "type": "image_template",
                                "priority": 1
                            })
                        elif strategy_type == "position" and "fallback" in config:
                            strategies.append({
                                "type": "coordinates",
                                "x": config["fallback"][0],
                                "y": config["fallback"][1],
                                "priority": 2
                            })
                        elif strategy_type == "text" and "text" in config:
                            strategies.append({
                                "type": "text",
                                "text": config["text"],
                                "priority": 3
                            })
                else:
                    # 如果没有locate_by，使用旧的逻辑
                    default_strategy = {
                        "type": "unknown"  # 会根据配置内容自动判断类型
                    }

                    if "template_path" in config:
                        default_strategy["type"] = "image_template"
                    elif "ui_type" in config:
                        default_strategy["type"] = "ui_feature"
                        default_strategy["ui_type"] = config["ui_type"]
                    elif "text" in config:
                        default_strategy["type"] = "text"
                        default_strategy["text"] = config["text"]
                    elif "resource-id" in config:
                        default_strategy["type"] = "resource-id"
                        default_strategy["resource-id"] = config["resource-id"]
                    elif "class" in config:
                        default_strategy["type"] = "class"
                        default_strategy["class"] = config["class"]
                    elif "fallback" in config:
                        default_strategy["type"] = "coordinates"
                        default_strategy["x"] = config["fallback"][0]
                        default_strategy["y"] = config["fallback"][1]

                    strategies = [default_strategy]
            
            # 按优先级排序策略
            strategies.sort(key=lambda x: x.get("priority", 999))
            
            # 尝试每个策略
            for strategy in strategies:
                try:
                    element_result = self.locator.locate_by_strategy(self.device, strategy, config)
                    if element_result:
                        logger.debug(f"成功使用 {strategy.get('type', 'default')} 策略定位 {config.get('name', '元素')}")
                        return element_result
                except Exception as e:
                    logger.debug(f"使用 {strategy.get('type', 'default')} 策略定位 {config.get('name', '元素')} 失败: {str(e)}")
            
            # 使用备用坐标
            if "fallback" in config:
                x, y = config["fallback"]
                logger.debug(f"使用备用坐标: ({x}, {y})")
                return {"x": x, "y": y, "type": "coordinates"}
            
            logger.warning(f"所有策略均无法定位元素: {config.get('name', '未命名元素')}")
            return None
            
        except Exception as e:
            logger.error(f"查找元素异常: {str(e)}")
            traceback.print_exc()
            return None
    
    def click_element(self, element_id_or_config: Union[str, Dict]) -> bool:
        """
        点击元素
        
        Args:
            element_id_or_config: 元素ID或配置
            
        Returns:
            是否点击成功
        """
        try:
            element = self.find_element(element_id_or_config)
            if not element:
                logger.warning(f"无法找到元素: {element_id_or_config}")
                return False
                
            # 获取元素配置
            config = element
            if isinstance(element_id_or_config, str):
                config = self.config_cache[element_id_or_config]
                
            # 处理content_area和random_in_content_area
            if 'content_area' in config and 'click_strategy' in config and config['click_strategy'].get('random_in_content_area', False):
                content_area = config['content_area']
                left = content_area.get('left', 0)
                right = content_area.get('right', self.device.screen_width)
                top = content_area.get('top', 0)
                bottom = content_area.get('bottom', self.device.screen_height)
                
                # 生成content_area内的随机坐标
                x = random.randint(left, right)
                y = random.randint(top, bottom)
                
                logger.info(f"在content_area内随机点击 ({x}, {y})")
                return self.device.tap(x, y)
                
            # 处理普通元素点击
            if isinstance(element, dict) and "type" in element:
                if element["type"] == "coordinates":
                    # 确保坐标是整数类型（处理numpy类型）
                    x = int(element["x"])
                    y = int(element["y"])
                    logger.debug(f"点击坐标: ({x}, {y})")
                    return self.device.tap(x, y)
                elif element["type"] == "node":
                    logger.debug(f"点击节点: {element.get('id', 'unknown')}")
                    node = element.get("node")
                    if node is not None and hasattr(node, "Click_events") and callable(node.Click_events):
                        try:
                            return bool(node.Click_events())
                        except Exception as e:
                            logger.error(f"节点点击异常: {str(e)}")
                            return False
                    else:
                        logger.error("节点不存在或不支持Click_events方法")
                        return False
                else:
                    logger.error(f"未知的元素类型: {element['type']}")
                    return False
            else:
                logger.error(f"无法处理未知类型的元素: {element}")
                return False
                
        except Exception as e:
            logger.error(f"点击元素异常: {str(e)}")
            return False

    def find_page_indicators(self, page_type: str, min_matches: int = 1, fast_mode: bool = False) -> List[Dict[str, Any]]:
        """
        查找页面指示器

        Args:
            page_type: 页面类型
            min_matches: 最小匹配数量
            fast_mode: 快速模式

        Returns:
            找到的指示器列表
        """
        try:
            # 页面指示器配置
            page_indicators = {
                "home": ["home_tab_selected", "recommend_tab_selected", "channel_shoes_selected", "channel_fashion_selected"],
                "detail": ["detail_action_bar", "comment_list"],
                "discovery": ["hot_tab_selected"],
                "profile": ["profile_avatar", "profile_follower_info"],
                "search": ["search_input", "search_button"],
                "author": ["follow_button", "author_info"],
                "message": ["message"],
                "hot": ["hot_tab_selected"],
                "publish": ["publish"]
            }

            indicators = page_indicators.get(page_type, [])
            found_indicators = []

            for indicator in indicators:
                element = self.find_element(indicator)
                if element:
                    found_indicators.append({
                        "element_id": indicator,
                        "result": element,
                        "confidence": 0.9  # 模拟置信度
                    })
                    if len(found_indicators) >= min_matches:
                        break

            if found_indicators:
                logger.debug(f"找到页面指示器: {page_type}, 数量: {len(found_indicators)}")
            else:
                logger.debug(f"未找到页面指示器: {page_type}")

            return found_indicators

        except Exception as e:
            logger.error(f"查找页面指示器异常: {page_type}, 错误: {e}")
            return []

    def click_coordinates(self, coordinates: Tuple[int, int], description: str = "", no_offset: bool = True) -> bool:
        """
        点击坐标

        Args:
            coordinates: 坐标 (x, y)
            description: 描述
            no_offset: 是否不使用偏移

        Returns:
            是否点击成功
        """
        try:
            x, y = coordinates

            if hasattr(self.device, 'click'):
                result = self.device.click(x, y)
                logger.debug(f"点击坐标: ({x}, {y}) - {description}, 结果: {result}")
                return bool(result)
            else:
                logger.warning(f"设备不支持click方法")
                return False

        except Exception as e:
            logger.error(f"点击坐标异常: {coordinates}, 错误: {e}")
            return False

    def find_topmost_element_by_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        查找最上方的元素

        Args:
            template_name: 模板名称

        Returns:
            最上方元素的信息字典，如果未找到则返回None
        """
        try:
            # 获取元素配置
            element_config = self.config_cache.get(template_name)
            if not element_config:
                logger.warning(f"未找到元素配置: {template_name}")
                return None

            # 获取模板路径
            template_path = element_config.get("template_path", "")
            if not template_path:
                logger.warning(f"元素 {template_name} 缺少template_path配置")
                return None

            # 获取阈值
            threshold = element_config.get("threshold", 0.6)

            # 获取当前屏幕截图
            screenshot = self.device.take_capture_compress()
            if not screenshot:
                logger.error("无法获取屏幕截图")
                return None

            # 将截图数据转换为图像
            import numpy as np
            import cv2

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(screenshot, np.uint8)
            # 解码图像
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if image is None:
                logger.error("无法解码屏幕截图")
                return None

            # 使用图像定位器查找所有匹配
            if self.image_locator:
                all_matches = self.image_locator.find_all_matches(
                    image=image,
                    template=template_path,
                    threshold=threshold,
                    max_results=10
                )

                if all_matches:
                    # 按Y坐标排序，选择最上方的（Y坐标最小的）
                    all_matches.sort(key=lambda x: x.get("center", [0, 0])[1])
                    topmost_match = all_matches[0]

                    center_x = topmost_match.get("center", [0, 0])[0]
                    center_y = topmost_match.get("center", [0, 0])[1]

                    logger.info(f"找到最上方的 {template_name} at ({center_x}, {center_y})")
                    return {
                        "x": center_x,
                        "y": center_y,
                        "type": "coordinates",
                        "bounds": topmost_match.get("position", [])
                    }
                else:
                    logger.debug(f"未找到匹配的模板: {template_name}")
                    return None
            else:
                logger.warning("图像定位器不可用")
                return None

        except Exception as e:
            logger.error(f"查找最上方元素异常: {template_name}, 错误: {e}")
            return None

    def click_coordinates(self, element_info: Dict[str, Any], description: str = "", no_offset: bool = False) -> bool:
        """
        点击指定坐标

        Args:
            element_info: 包含坐标信息的字典
            description: 操作描述
            no_offset: 是否不使用偏移

        Returns:
            是否点击成功
        """
        try:
            x = element_info.get("x", 0)
            y = element_info.get("y", 0)

            if x == 0 and y == 0:
                logger.warning(f"无效的坐标: {element_info}")
                return False

            # 执行点击
            result = self.device.click(x, y)
            if result:
                logger.info(f"✅ 成功点击坐标 ({x}, {y}) - {description}")
            else:
                logger.warning(f"⚠️ 点击坐标失败 ({x}, {y}) - {description}")

            return result

        except Exception as e:
            logger.error(f"点击坐标异常: {e}")
            return False

    def verify_click(self, config: Dict, element_result: Any) -> bool:
        """
        验证点击是否成功
        
        Args:
            config: 元素配置
            element_result: 定位结果
            
        Returns:
            是否验证成功
        """
        verify_config = config.get("verify")
        if not verify_config:
            return True
        
        verify_type = verify_config.get("type")
        timeout = verify_config.get("timeout", 2000)
        
        try:
            # 等待一段时间让UI变化
            time.sleep(0.5)
            
            if verify_type == "element_appear":
                # 验证某个元素出现
                target = verify_config.get("target_element")
                if not target:
                    return True
                
                return self.find_element(target) is not None
            
            elif verify_type == "element_disappear":
                # 验证某个元素消失
                target = verify_config.get("target_element")
                if not target:
                    return True
                
                return self.find_element(target) is None
            
            elif verify_type == "state_change":
                # 验证元素状态变化
                # 这里简化处理，可以根据需要扩展
                return True
            
            return True
            
        except Exception as e:
            logger.error(f"验证点击异常: {str(e)}")
            return True  # 验证失败不应该阻止操作流程 