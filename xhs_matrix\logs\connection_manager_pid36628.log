2025-07-29 02:39:36,624 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔧 创建增强RPC客户端: 10.0.0.127:11050
2025-07-29 02:39:36,624 - xhs_matrix.core.enhanced_rpc_client - INFO - 🏷️ 容器名称已设置: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 02:39:36,624 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔒 安全上下文已设置
2025-07-29 02:39:36,624 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 所有安全参数完整
2025-07-29 02:39:36,624 - xhs_matrix.core.enhanced_rpc_client - INFO - 🚀 开始连接设备流程: 10.0.0.127:11050
2025-07-29 02:39:36,625 - xhs_matrix.core.enhanced_rpc_client - INFO - 📋 连接配置: 最大重试3次, 连接超时3秒, 重试间隔5秒
2025-07-29 02:39:36,625 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔗 [第1/3次] 开始连接尝试: 10.0.0.127:11050
2025-07-29 02:39:36,625 - xhs_matrix.core.enhanced_rpc_client - INFO - SDK动态库路径: d:\xhs_matrix_v2.0.0\demo_py_x64\lib\libmytrpc.dll
2025-07-29 02:39:36,626 - xhs_matrix.core.enhanced_rpc_client - INFO - 检测到SDK版本: 10
2025-07-29 02:39:36,657 - xhs_matrix.core.enhanced_rpc_client - INFO - 工作模式设置成功: ACCESSIBILITY_OFF
2025-07-29 02:39:36,658 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ [第1次] 设备连接成功: 10.0.0.127:11050
2025-07-29 02:39:36,658 - xhs_matrix.core.enhanced_rpc_client - INFO - ⏱️ 连接耗时: 本次尝试0.03秒, 总耗时0.03秒
2025-07-29 02:39:36,662 - xhs_matrix.core.health_monitor_manager - INFO - 全局健康监控管理器初始化完成
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitoring - INFO - 创建连接健康监控器: 8849b789-dda3-403e-a367-943444f4fa31 -> 10.0.0.127:11050[5]
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitoring - INFO - 健康监控线程启动: 8849b789-dda3-403e-a367-943444f4fa31
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitoring - INFO - 健康监控启动成功: 8849b789-dda3-403e-a367-943444f4fa31
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitoring - INFO - 健康监控等待初始延迟 10 秒，确保连接稳定...
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitor_manager - INFO - ✅ 注册健康监控器: 10.0.0.127:5 (任务: 8849b789-dda3-403e-a367-943444f4fa31)
2025-07-29 02:39:36,663 - xhs_matrix.core.health_monitor_manager - INFO - 📊 当前活跃监控器总数: 1
2025-07-29 02:39:36,664 - xhs_matrix.core.health_monitor_bridge - INFO - 健康监控桥接器初始化完成: D:\xhs_matrix_v2.0.0\xhs_matrix\logs\health_bridge
2025-07-29 02:39:47,242 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (262, 940) -> (275, 551), 时长: 1.2397742375435576秒
2025-07-29 02:39:48,482 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (262, 940) 到 (275, 551), 持续 1.2397742375435576秒, 成功率: 100.0%
2025-07-29 02:39:51,494 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (235, 1140) -> (229, 591), 时长: 1.0872014214169015秒
2025-07-29 02:39:52,582 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (235, 1140) 到 (229, 591), 持续 1.0872014214169015秒, 成功率: 100.0%
2025-07-29 02:40:03,417 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (360, 256) -> (359, 640), 时长: 0.8秒
2025-07-29 02:40:04,218 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (360, 256) 到 (359, 640), 持续 0.8秒, 成功率: 100.0%
2025-07-29 02:40:09,869 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (473, 999) -> (498, 419), 时长: 0.8893757482115263秒
2025-07-29 02:40:10,760 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (473, 999) 到 (498, 419), 持续 0.8893757482115263秒, 成功率: 100.0%
2025-07-29 02:40:17,449 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 324) -> (144, 314), 时长: 0.8948445799753588秒
2025-07-29 02:40:18,344 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 324) 到 (144, 314), 持续 0.8948445799753588秒, 成功率: 100.0%
2025-07-29 02:40:20,354 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (167, 896) -> (167, 384), 时长: 1.0秒
2025-07-29 02:40:21,356 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (167, 896) 到 (167, 384), 持续 1.0秒, 成功率: 100.0%
2025-07-29 02:40:24,281 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (498, 896) -> (498, 384), 时长: 0.844秒
2025-07-29 02:40:25,126 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (498, 896) 到 (498, 384), 持续 0.844秒, 成功率: 100.0%
2025-07-29 02:40:29,465 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (361, 896) -> (361, 384), 时长: 0.757秒
2025-07-29 02:40:30,223 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (361, 896) 到 (361, 384), 持续 0.757秒, 成功率: 100.0%
2025-07-29 02:40:30,625 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (432, 384) -> (427, 896), 时长: 1.2670293169385165秒
2025-07-29 02:40:31,893 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (432, 384) 到 (427, 896), 持续 1.2670293169385165秒, 成功率: 100.0%
2025-07-29 02:40:33,030 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (484, 384) -> (460, 896), 时长: 1.2333641785470315秒
2025-07-29 02:40:34,264 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (484, 384) 到 (460, 896), 持续 1.2333641785470315秒, 成功率: 100.0%
2025-07-29 02:40:35,536 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (215, 384) -> (245, 896), 时长: 0.7544902575577224秒
2025-07-29 02:40:36,295 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (215, 384) 到 (245, 896), 持续 0.7544902575577224秒, 成功率: 100.0%
2025-07-29 02:40:37,410 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (512, 384) -> (524, 896), 时长: 1.1114715467095768秒
2025-07-29 02:40:38,522 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (512, 384) 到 (524, 896), 持续 1.1114715467095768秒, 成功率: 100.0%
2025-07-29 02:40:39,846 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (462, 384) -> (448, 896), 时长: 1.2818961056772438秒
2025-07-29 02:40:41,132 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (462, 384) 到 (448, 896), 持续 1.2818961056772438秒, 成功率: 100.0%
