2025-07-29 02:18:23,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔧 创建增强RPC客户端: 10.0.0.127:11050
2025-07-29 02:18:23,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🏷️ 容器名称已设置: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 02:18:23,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔒 安全上下文已设置
2025-07-29 02:18:23,274 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 所有安全参数完整
2025-07-29 02:18:23,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🚀 开始连接设备流程: 10.0.0.127:11050
2025-07-29 02:18:23,275 - xhs_matrix.core.enhanced_rpc_client - INFO - 📋 连接配置: 最大重试3次, 连接超时3秒, 重试间隔5秒
2025-07-29 02:18:23,275 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔗 [第1/3次] 开始连接尝试: 10.0.0.127:11050
2025-07-29 02:18:23,276 - xhs_matrix.core.enhanced_rpc_client - INFO - SDK动态库路径: d:\xhs_matrix_v2.0.0\demo_py_x64\lib\libmytrpc.dll
2025-07-29 02:18:23,277 - xhs_matrix.core.enhanced_rpc_client - INFO - 检测到SDK版本: 10
2025-07-29 02:18:23,291 - xhs_matrix.core.enhanced_rpc_client - INFO - 工作模式设置成功: ACCESSIBILITY_OFF
2025-07-29 02:18:23,292 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ [第1次] 设备连接成功: 10.0.0.127:11050
2025-07-29 02:18:23,292 - xhs_matrix.core.enhanced_rpc_client - INFO - ⏱️ 连接耗时: 本次尝试0.02秒, 总耗时0.02秒
2025-07-29 02:18:23,294 - xhs_matrix.core.health_monitor_manager - INFO - 全局健康监控管理器初始化完成
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitoring - INFO - 创建连接健康监控器: 898861b5-eefd-4fec-840f-d471096a175b -> 10.0.0.127:11050[5]
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitoring - INFO - 健康监控线程启动: 898861b5-eefd-4fec-840f-d471096a175b
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitoring - INFO - 健康监控启动成功: 898861b5-eefd-4fec-840f-d471096a175b
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitoring - INFO - 健康监控等待初始延迟 10 秒，确保连接稳定...
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitor_manager - INFO - ✅ 注册健康监控器: 10.0.0.127:5 (任务: 898861b5-eefd-4fec-840f-d471096a175b)
2025-07-29 02:18:23,295 - xhs_matrix.core.health_monitor_manager - INFO - 📊 当前活跃监控器总数: 1
2025-07-29 02:18:23,296 - xhs_matrix.core.health_monitor_bridge - INFO - 健康监控桥接器初始化完成: D:\xhs_matrix_v2.0.0\xhs_matrix\logs\health_bridge
2025-07-29 02:18:35,051 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (371, 796) -> (386, 182), 时长: 0.8096536434201224秒
2025-07-29 02:18:35,862 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (371, 796) 到 (386, 182), 持续 0.8096536434201224秒, 成功率: 100.0%
2025-07-29 02:18:42,541 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 331) -> (144, 364), 时长: 0.9482767202964774秒
2025-07-29 02:18:43,491 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 331) 到 (144, 364), 持续 0.9482767202964774秒, 成功率: 100.0%
2025-07-29 02:18:45,901 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (213, 896) -> (213, 384), 时长: 0.731秒
2025-07-29 02:18:46,634 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (213, 896) 到 (213, 384), 持续 0.731秒, 成功率: 100.0%
2025-07-29 02:18:49,246 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (426, 896) -> (426, 384), 时长: 0.598秒
2025-07-29 02:18:49,845 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (426, 896) 到 (426, 384), 持续 0.598秒, 成功率: 100.0%
2025-07-29 02:18:52,954 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (309, 896) -> (309, 384), 时长: 0.74秒
2025-07-29 02:18:53,695 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (309, 896) 到 (309, 384), 持续 0.74秒, 成功率: 100.0%
2025-07-29 02:18:56,910 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (332, 896) -> (332, 384), 时长: 0.997秒
2025-07-29 02:18:57,908 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (332, 896) 到 (332, 384), 持续 0.997秒, 成功率: 100.0%
2025-07-29 02:19:03,332 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (220, 896) -> (220, 384), 时长: 0.76秒
2025-07-29 02:19:04,093 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (220, 896) 到 (220, 384), 持续 0.76秒, 成功率: 100.0%
2025-07-29 02:19:05,900 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (557, 896) -> (557, 384), 时长: 0.737秒
2025-07-29 02:19:06,646 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (557, 896) 到 (557, 384), 持续 0.737秒, 成功率: 100.0%
2025-07-29 02:19:16,417 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (360, 256) -> (360, 640), 时长: 0.8秒
2025-07-29 02:19:17,218 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (360, 256) 到 (360, 640), 持续 0.8秒, 成功率: 100.0%
2025-07-29 02:19:33,183 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (263, 785) -> (245, 225), 时长: 0.9688157871351839秒
2025-07-29 02:19:34,153 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (263, 785) 到 (245, 225), 持续 0.9688157871351839秒, 成功率: 100.0%
2025-07-29 02:19:35,364 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (186, 961) -> (228, 428), 时长: 1.6273177412975313秒
2025-07-29 02:19:36,994 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (186, 961) 到 (228, 428), 持续 1.6273177412975313秒, 成功率: 100.0%
2025-07-29 02:19:43,386 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 350) -> (144, 311), 时长: 0.9709132662751466秒
2025-07-29 02:19:44,358 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 350) 到 (144, 311), 持续 0.9709132662751466秒, 成功率: 100.0%
2025-07-29 02:19:46,875 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (241, 896) -> (241, 384), 时长: 0.876秒
2025-07-29 02:19:47,752 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (241, 896) 到 (241, 384), 持续 0.876秒, 成功率: 100.0%
2025-07-29 02:19:49,562 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (518, 896) -> (518, 384), 时长: 0.618秒
2025-07-29 02:19:50,181 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (518, 896) 到 (518, 384), 持续 0.618秒, 成功率: 100.0%
2025-07-29 02:19:53,215 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (567, 896) -> (567, 384), 时长: 0.529秒
2025-07-29 02:19:53,745 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (567, 896) 到 (567, 384), 持续 0.529秒, 成功率: 100.0%
2025-07-29 02:20:01,990 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (294, 384) -> (294, 896), 时长: 0.989秒
2025-07-29 02:20:02,980 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (294, 384) 到 (294, 896), 持续 0.989秒, 成功率: 100.0%
2025-07-29 02:20:06,198 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (405, 896) -> (405, 384), 时长: 0.95秒
2025-07-29 02:20:07,149 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (405, 896) 到 (405, 384), 持续 0.95秒, 成功率: 100.0%
2025-07-29 02:20:21,590 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (478, 857) -> (481, 222), 时长: 1.4254076762317864秒
2025-07-29 02:20:23,016 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (478, 857) 到 (481, 222), 持续 1.4254076762317864秒, 成功率: 100.0%
2025-07-29 02:20:24,122 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (271, 958) -> (292, 526), 时长: 0.47045755140350193秒
2025-07-29 02:20:24,594 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (271, 958) 到 (292, 526), 持续 0.47045755140350193秒, 成功率: 100.0%
2025-07-29 02:20:26,000 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (556, 1118) -> (526, 628), 时长: 1.080020693875012秒
2025-07-29 02:20:27,081 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (556, 1118) 到 (526, 628), 持续 1.080020693875012秒, 成功率: 100.0%
2025-07-29 02:20:34,184 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 306) -> (144, 249), 时长: 1.224943777429522秒
2025-07-29 02:20:35,410 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 306) 到 (144, 249), 持续 1.224943777429522秒, 成功率: 100.0%
2025-07-29 02:20:37,646 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 352) -> (144, 315), 时长: 0.8261388231633826秒
2025-07-29 02:20:38,473 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 352) 到 (144, 315), 持续 0.8261388231633826秒, 成功率: 100.0%
2025-07-29 02:20:40,588 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (215, 896) -> (215, 384), 时长: 0.774秒
2025-07-29 02:20:41,363 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (215, 896) 到 (215, 384), 持续 0.774秒, 成功率: 100.0%
2025-07-29 02:20:43,674 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (431, 896) -> (431, 384), 时长: 0.58秒
2025-07-29 02:20:44,256 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (431, 896) 到 (431, 384), 持续 0.58秒, 成功率: 100.0%
2025-07-29 02:20:47,369 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (394, 384) -> (394, 896), 时长: 0.909秒
2025-07-29 02:20:48,279 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (394, 384) 到 (394, 896), 持续 0.909秒, 成功率: 100.0%
2025-07-29 02:20:48,696 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (419, 384) -> (413, 896), 时长: 1.0449777428351081秒
2025-07-29 02:20:49,741 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (419, 384) 到 (413, 896), 持续 1.0449777428351081秒, 成功率: 100.0%
2025-07-29 02:20:50,997 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (377, 384) -> (410, 896), 时长: 0.9880236811858921秒
2025-07-29 02:20:51,986 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (377, 384) 到 (410, 896), 持续 0.9880236811858921秒, 成功率: 100.0%
2025-07-29 02:20:52,908 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (389, 384) -> (364, 896), 时长: 1.315526609220571秒
2025-07-29 02:20:54,225 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (389, 384) 到 (364, 896), 持续 1.315526609220571秒, 成功率: 100.0%
2025-07-29 02:20:55,370 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (223, 384) -> (212, 896), 时长: 0.9454305751503113秒
2025-07-29 02:20:56,316 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (223, 384) 到 (212, 896), 持续 0.9454305751503113秒, 成功率: 100.0%
2025-07-29 02:20:57,499 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (335, 384) -> (344, 896), 时长: 0.7705902018989204秒
2025-07-29 02:20:58,271 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (335, 384) 到 (344, 896), 持续 0.7705902018989204秒, 成功率: 100.0%
2025-07-29 02:21:30,033 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (286, 896) -> (286, 384), 时长: 0.772秒
2025-07-29 02:21:30,806 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (286, 896) 到 (286, 384), 持续 0.772秒, 成功率: 100.0%
2025-07-29 02:21:32,413 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (227, 896) -> (227, 384), 时长: 0.974秒
2025-07-29 02:21:33,388 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (227, 896) 到 (227, 384), 持续 0.974秒, 成功率: 100.0%
2025-07-29 02:21:47,701 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (353, 770) -> (372, 301), 时长: 0.920849698322262秒
2025-07-29 02:21:48,623 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (353, 770) 到 (372, 301), 持续 0.920849698322262秒, 成功率: 100.0%
2025-07-29 02:21:49,727 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (416, 934) -> (368, 486), 时长: 1.1420596280716933秒
2025-07-29 02:21:50,871 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (416, 934) 到 (368, 486), 持续 1.1420596280716933秒, 成功率: 100.0%
2025-07-29 02:21:52,780 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (258, 972) -> (281, 401), 时长: 1.1484955679954139秒
2025-07-29 02:21:53,930 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (258, 972) 到 (281, 401), 持续 1.1484955679954139秒, 成功率: 100.0%
2025-07-29 02:22:00,879 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 331) -> (144, 359), 时长: 0.8034564835139277秒
2025-07-29 02:22:01,683 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 331) 到 (144, 359), 持续 0.8034564835139277秒, 成功率: 100.0%
2025-07-29 02:22:04,016 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 356) -> (144, 354), 时长: 1.3068497006247217秒
2025-07-29 02:22:05,324 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 356) 到 (144, 354), 持续 1.3068497006247217秒, 成功率: 100.0%
2025-07-29 02:22:07,923 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 251) -> (144, 293), 时长: 1.2967268593348193秒
2025-07-29 02:22:09,221 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 251) 到 (144, 293), 持续 1.2967268593348193秒, 成功率: 100.0%
