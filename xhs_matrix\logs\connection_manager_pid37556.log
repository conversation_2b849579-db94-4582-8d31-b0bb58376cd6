2025-07-29 03:22:51,970 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔧 创建增强RPC客户端: 10.0.0.127:11050
2025-07-29 03:22:51,970 - xhs_matrix.core.enhanced_rpc_client - INFO - 🏷️ 容器名称已设置: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 03:22:51,970 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔒 安全上下文已设置
2025-07-29 03:22:51,970 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 所有安全参数完整
2025-07-29 03:22:51,970 - xhs_matrix.core.enhanced_rpc_client - INFO - 🚀 开始连接设备流程: 10.0.0.127:11050
2025-07-29 03:22:51,971 - xhs_matrix.core.enhanced_rpc_client - INFO - 📋 连接配置: 最大重试3次, 连接超时3秒, 重试间隔5秒
2025-07-29 03:22:51,971 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔗 [第1/3次] 开始连接尝试: 10.0.0.127:11050
2025-07-29 03:22:51,971 - xhs_matrix.core.enhanced_rpc_client - INFO - SDK动态库路径: d:\xhs_matrix_v2.0.0\demo_py_x64\lib\libmytrpc.dll
2025-07-29 03:22:51,972 - xhs_matrix.core.enhanced_rpc_client - INFO - 检测到SDK版本: 10
2025-07-29 03:22:51,989 - xhs_matrix.core.enhanced_rpc_client - INFO - 工作模式设置成功: ACCESSIBILITY_OFF
2025-07-29 03:22:51,989 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ [第1次] 设备连接成功: 10.0.0.127:11050
2025-07-29 03:22:51,990 - xhs_matrix.core.enhanced_rpc_client - INFO - ⏱️ 连接耗时: 本次尝试0.02秒, 总耗时0.02秒
2025-07-29 03:22:51,995 - xhs_matrix.core.health_monitor_manager - INFO - 全局健康监控管理器初始化完成
2025-07-29 03:22:51,995 - xhs_matrix.core.health_monitoring - INFO - 创建连接健康监控器: 1b9d285a-2e3b-4c0c-835e-0e8b691a0523 -> 10.0.0.127:11050[5]
2025-07-29 03:22:51,996 - xhs_matrix.core.health_monitoring - INFO - 健康监控线程启动: 1b9d285a-2e3b-4c0c-835e-0e8b691a0523
2025-07-29 03:22:51,996 - xhs_matrix.core.health_monitoring - INFO - 健康监控启动成功: 1b9d285a-2e3b-4c0c-835e-0e8b691a0523
2025-07-29 03:22:51,996 - xhs_matrix.core.health_monitor_manager - INFO - ✅ 注册健康监控器: 10.0.0.127:5 (任务: 1b9d285a-2e3b-4c0c-835e-0e8b691a0523)
2025-07-29 03:22:51,996 - xhs_matrix.core.health_monitor_manager - INFO - 📊 当前活跃监控器总数: 1
2025-07-29 03:22:51,996 - xhs_matrix.core.health_monitoring - INFO - 健康监控等待初始延迟 10 秒，确保连接稳定...
2025-07-29 03:22:51,997 - xhs_matrix.core.health_monitor_bridge - INFO - 健康监控桥接器初始化完成: D:\xhs_matrix_v2.0.0\xhs_matrix\logs\health_bridge
2025-07-29 03:23:07,019 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (360, 256) -> (341, 640), 时长: 0.8秒
2025-07-29 03:23:07,821 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (360, 256) 到 (341, 640), 持续 0.8秒, 成功率: 100.0%
2025-07-29 03:23:15,339 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (272, 1007) -> (273, 462), 时长: 1.0272699860181445秒
2025-07-29 03:23:16,367 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (272, 1007) 到 (273, 462), 持续 1.0272699860181445秒, 成功率: 100.0%
2025-07-29 03:23:18,778 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (201, 870) -> (200, 305), 时长: 0.7746075529477177秒
2025-07-29 03:23:19,554 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (201, 870) 到 (200, 305), 持续 0.7746075529477177秒, 成功率: 100.0%
2025-07-29 03:23:22,464 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (213, 888) -> (204, 478), 时长: 0.6222915785228401秒
2025-07-29 03:23:23,088 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (213, 888) 到 (204, 478), 持续 0.6222915785228401秒, 成功率: 100.0%
2025-07-29 03:23:30,305 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 309) -> (144, 299), 时长: 0.8245845137797975秒
2025-07-29 03:23:31,131 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 309) 到 (144, 299), 持续 0.8245845137797975秒, 成功率: 100.0%
2025-07-29 03:23:33,552 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 344) -> (144, 388), 时长: 1.0698910502430257秒
2025-07-29 03:23:34,623 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 344) 到 (144, 388), 持续 1.0698910502430257秒, 成功率: 100.0%
2025-07-29 03:23:36,736 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (269, 896) -> (269, 384), 时长: 0.83秒
2025-07-29 03:23:37,567 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (269, 896) 到 (269, 384), 持续 0.83秒, 成功率: 100.0%
2025-07-29 03:23:39,883 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (464, 384) -> (464, 896), 时长: 0.981秒
2025-07-29 03:23:40,865 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (464, 384) 到 (464, 896), 持续 0.981秒, 成功率: 100.0%
2025-07-29 03:23:43,902 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (454, 896) -> (454, 384), 时长: 0.995秒
2025-07-29 03:23:44,899 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (454, 896) 到 (454, 384), 持续 0.995秒, 成功率: 100.0%
2025-07-29 03:23:45,295 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (325, 384) -> (317, 896), 时长: 1.117008277964218秒
2025-07-29 03:23:46,414 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (325, 384) 到 (317, 896), 持续 1.117008277964218秒, 成功率: 100.0%
2025-07-29 03:23:47,565 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (328, 384) -> (343, 896), 时长: 1.346536871159709秒
2025-07-29 03:23:48,913 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (328, 384) 到 (343, 896), 持续 1.346536871159709秒, 成功率: 100.0%
2025-07-29 03:23:50,287 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (254, 384) -> (238, 896), 时长: 0.9574145714000631秒
2025-07-29 03:23:51,246 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (254, 384) 到 (238, 896), 持续 0.9574145714000631秒, 成功率: 100.0%
2025-07-29 03:23:52,496 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (390, 384) -> (356, 896), 时长: 1.3285013518829623秒
2025-07-29 03:23:53,826 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (390, 384) 到 (356, 896), 持续 1.3285013518829623秒, 成功率: 100.0%
2025-07-29 03:23:54,860 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (349, 384) -> (371, 896), 时长: 1.183615525795915秒
2025-07-29 03:23:56,044 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (349, 384) 到 (371, 896), 持续 1.183615525795915秒, 成功率: 100.0%
2025-07-29 03:24:27,441 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (458, 896) -> (458, 384), 时长: 0.816秒
2025-07-29 03:24:28,258 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (458, 896) 到 (458, 384), 持续 0.816秒, 成功率: 100.0%
2025-07-29 03:24:29,464 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (465, 896) -> (465, 384), 时长: 0.556秒
2025-07-29 03:24:30,021 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (465, 896) 到 (465, 384), 持续 0.556秒, 成功率: 100.0%
2025-07-29 03:24:43,049 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (252, 797) -> (211, 367), 时长: 1.0455713337326926秒
2025-07-29 03:24:44,095 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (252, 797) 到 (211, 367), 持续 1.0455713337326926秒, 成功率: 100.0%
