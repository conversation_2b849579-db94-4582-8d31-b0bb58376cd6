#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
关键词搜索控制器
负责执行搜索和评论等互动任务
"""

import os
import sys
import time
import random
import json
import traceback
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目根目录到系统路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 新系统独立导入
from xhs_matrix.core.enhanced_rpc_client import OptimizedMytRpc as XHSDevice
from xhs_matrix.legacy.controllers.base_controller import BaseController
from xhs_matrix.common.logger import logger

class InteractionController(BaseController):
    """
    互动评论控制器
    负责执行搜索和评论等互动任务
    """
    
    # 小红书包名
    PACKAGE_NAME = "com.xingin.xhs"
    
    # 统一屏幕尺寸
    DEFAULT_SCREEN_WIDTH = 720
    DEFAULT_SCREEN_HEIGHT = 1280
    
    # 互动类型
    INTERACTION_TYPE_COMMENT = "comment"          # 评论
    INTERACTION_TYPE_LIKE = "like"                # 点赞
    INTERACTION_TYPE_COLLECT = "collect"          # 收藏
    INTERACTION_TYPE_FOLLOW = "follow"            # 关注
    
    def __init__(self, device: XHSDevice, config_path: Optional[str] = None, config_json_path: Optional[str] = None):
        """
        初始化交互任务控制器
        
        Args:
            device: 设备对象
            config_path: 元素配置文件路径
            config_json_path: json配置文件路径（可选）
        """
        super().__init__(device, config_path)
        
        # 交互相关状态
        self.current_page = "unknown"  # 当前所在页面
        self.browse_count = 0          # 浏览笔记数量
        self.like_count = 0            # 点赞数量
        self.comment_count = 0         # 评论数量
        self.collect_count = 0         # 收藏数量
        self.follow_count = 0          # 关注数量
        self.search_count = 0          # 搜索次数
        self.is_terminated = False     # 添加终止标记
        
        # 重要属性初始化
        self.search_keywords = []      # 搜索关键词列表 
        self.last_search_keyword = None  # 最后一次搜索使用的关键词
        
        # 读取评论模板文件
        self.comment_templates = self._load_comment_templates()
        
        # 读取目标用户列表
        self.target_users = self._load_target_users()
        
        # 读取表情包模板（sticker_templates），严格对齐account_nurturing_controller.py
        self.sticker_templates = []
        if config_json_path and os.path.exists(config_json_path):
            try:
                with open(config_json_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.sticker_templates = config_data.get('sticker_templates', [])
            except Exception as e:
                logger.warning(f"加载sticker_templates失败: {e}")
        logger.info(f"[INIT] 当前sticker_templates数量: {len(self.sticker_templates)}")

        # 追评功能相关属性
        self.enable_follow_up = False
        self.follow_up_start_time = None
        self.follow_up_duration = 0  # 追评操作实际耗时
        self.total_follow_up_time = 0  # 当前关键词累积的追评时间
        self.follow_up_templates = []  # 追评文本模板列表
    
    def get_specific_status(self) -> Dict[str, Any]:
        """
        获取互动评论特定状态
        
        Returns:
            状态字典
        """
        return {
            "current_page": self.current_page,
            "comment_count": self.comment_count,
            "like_count": self.like_count,
            "collect_count": self.collect_count,
            "follow_count": self.follow_count,
            "search_count": self.search_count
        }
    
    def _load_comment_templates(self) -> List[str]:
        """
        加载评论模板
        
        注意：默认模板已弃用，请通过execute方法的comments参数传入评论内容
        
        Returns:
            评论模板列表
        """
        # 始终使用空列表，不再从文件加载默认模板
        # 这确保只使用用户通过GUI上传的评论模板
        return []
    
    def _load_search_keywords(self) -> List[str]:
        """
        加载搜索关键词
        
        Returns:
            搜索关键词列表
        """
        keywords = []  # 移除默认值，使用空列表
        
        # 尝试从文件加载
        keyword_file = Path(project_root) / "configs" / "keywords.txt"
        if keyword_file.exists():
            try:
                with open(keyword_file, "r", encoding="utf-8") as f:
                    file_keywords = [line.strip() for line in f if line.strip()]
                    if file_keywords:
                        keywords = file_keywords
                        logger.info(f"已加载 {len(keywords)} 个搜索关键词")
            except Exception as e:
                logger.error(f"加载搜索关键词出错: {str(e)}")
        
        return keywords
    
    def _load_target_users(self) -> List[str]:
        """
        加载目标用户列表
        
        Returns:
            目标用户列表
        """
        users = []
        
        # 尝试从文件加载
        users_file = Path(project_root) / "configs" / "target_users.txt"
        if users_file.exists():
            try:
                with open(users_file, "r", encoding="utf-8") as f:
                    file_users = [line.strip() for line in f if line.strip()]
                    if file_users:
                        users = file_users
                        logger.info(f"已加载 {len(users)} 个目标用户")
            except Exception as e:
                logger.error(f"加载目标用户出错: {str(e)}")
        
        return users
    
    def _get_random_comment(self) -> Optional[str]:
        """
        获取随机评论
        
        Returns:
            随机评论内容，如果评论模板为空返回None
        """
        if not self.comment_templates:
            logger.warning("⚠️ 评论模板为空，无法随机选择评论")
            # 返回None，由其他逻辑决定如何处理空评论
            return None
        
        # 从评论模板中随机选择一条评论
        comment = random.choice(self.comment_templates)
        logger.info(f"已随机选择评论模板: {comment[:30]}..." if len(comment) > 30 else comment)
        return comment
    
    def _get_random_keyword(self) -> Optional[str]:
        """
        获取随机关键词
        
        Returns:
            随机关键词，如果没有可用关键词则返回None
        """
        # 首先检查是否存在search_keywords属性并且有内容
        if hasattr(self, 'search_keywords') and self.search_keywords:
            keyword = random.choice(self.search_keywords)
            logger.info(f"从search_keywords中随机选择关键词: {keyword}")
            return keyword
            
        # 如果没有关键词列表，返回None
        logger.warning("⚠️ 随机关键词功能已禁用，未找到有效的关键词列表")
        return None
    
    def detect_current_page(self, hint: Optional[str] = None) -> str:
        """
        检测当前页面类型
        
        Args:
            hint: 提示可能的页面类型，如果提供则优先检查该类型
            
        Returns:
            页面类型: home, detail, profile, search, search_results, comments, author, message, hot, publish, discovery, unknown
        """
        # 使用页面指示器进行更可靠的页面验证
        if not self.element_manager:
            logger.warning("⚠️ ElementManager未初始化，无法检测页面")
            return "unknown"

        # 如果提供了hint，先检查该类型
        if hint:
            logger.info(f"根据提示'{hint}'优先检查该类型")
            if hint == "home":
                home_indicators = self.element_manager.find_page_indicators('home', min_matches=1)
                if home_indicators:
                    logger.info("通过页面指示器确认当前在首页")
                    self.current_page = "home"
                    return "home"
            elif hint == "detail":
                detail_indicators = self.element_manager.find_page_indicators('detail', min_matches=1)
                if detail_indicators:
                    logger.info("通过页面指示器确认当前在详情页")
                    self.current_page = "detail"
                    return "detail"
            elif hint == "discovery":
                discovery_indicators = self.element_manager.find_page_indicators('discovery', min_matches=1)
                if discovery_indicators:
                    logger.info("通过页面指示器确认当前在发现视频页")
                    self.current_page = "discovery"
                    return "discovery"
            elif hint == "profile":
                profile_indicators = self.element_manager.find_page_indicators('profile', min_matches=1)
                if profile_indicators:
                    logger.info("通过页面指示器确认当前在个人主页")
                    self.current_page = "profile"
                    return "profile"
            elif hint == "search":
                search_indicators = self.element_manager.find_page_indicators('search', min_matches=1)
                if search_indicators:
                    logger.info("通过页面指示器确认当前在搜索页")
                    self.current_page = "search"
                    return "search"
            elif hint == "search_results":
                search_results_indicators = self.element_manager.find_page_indicators('search_results', min_matches=1)
                if search_results_indicators:
                    logger.info("通过页面指示器确认当前在搜索结果页")
                    self.current_page = "search_results"
                    return "search_results"
            elif hint == "comments":
                comments_indicators = self.element_manager.find_page_indicators('comments', min_matches=1)
                if comments_indicators:
                    logger.info("通过页面指示器确认当前在评论页")
                    self.current_page = "comments"
                    return "comments"
            elif hint == "author":
                author_indicators = self.element_manager.find_page_indicators('author', min_matches=1)
                if author_indicators:
                    logger.info("通过页面指示器确认当前在作者主页")
                    self.current_page = "author"
                    return "author"
            elif hint == "message":
                message_indicators = self.element_manager.find_page_indicators('message', min_matches=1)
                if message_indicators:
                    logger.info("通过页面指示器确认当前在消息页")
                    self.current_page = "message"
                    return "message"
            elif hint == "hot":
                hot_indicators = self.element_manager.find_page_indicators('hot', min_matches=1)
                if hot_indicators:
                    logger.info("通过页面指示器确认当前在热门页")
                    self.current_page = "hot"
                    return "hot"
            elif hint == "publish":
                publish_indicators = self.element_manager.find_page_indicators('publish', min_matches=1)
                if publish_indicators:
                    logger.info("通过页面指示器确认当前在发布页")
                    self.current_page = "publish"
                    return "publish"
            
            logger.info(f"提示的页面类型'{hint}'验证失败，继续检查其他类型")
        
        # 修改检查顺序，优先检查详情页和发现视频页
        
        # 检查是否在详情页
        detail_indicators = self.element_manager.find_page_indicators('detail', min_matches=1)
        if detail_indicators:
            logger.info("通过页面指示器确认当前在详情页")
            self.current_page = "detail"
            return "detail"
        
        # 检查是否在发现视频页
        discovery_indicators = self.element_manager.find_page_indicators('discovery', min_matches=1)
        if discovery_indicators:
            logger.info("通过页面指示器确认当前在发现视频页")
            self.current_page = "discovery"
            return "discovery"
        
        # 检查是否在首页
        home_indicators = self.element_manager.find_page_indicators('home', min_matches=1)
        if home_indicators:
            logger.info("通过页面指示器确认当前在首页")
            self.current_page = "home"
            return "home"
        
        # 检查是否在用户个人页
        profile_indicators = self.element_manager.find_page_indicators('profile', min_matches=1)
        if profile_indicators:
            logger.info("通过页面指示器确认当前在个人主页")
            self.current_page = "profile"
            return "profile"
        
        # 检查是否在搜索结果页（优先于普通搜索页检查）
        search_results_indicators = self.element_manager.find_page_indicators('search_results', min_matches=1)
        if search_results_indicators:
            logger.info("通过页面指示器确认当前在搜索结果页")
            self.current_page = "search_results"
            return "search_results"
        
        # 检查是否在搜索页
        search_indicators = self.element_manager.find_page_indicators('search', min_matches=1)
        if search_indicators:
            logger.info("通过页面指示器确认当前在搜索页")
            self.current_page = "search"
            return "search"
        
        # 检查是否在评论页
        comments_indicators = self.element_manager.find_page_indicators('comments', min_matches=1)
        if comments_indicators:
            logger.info("通过页面指示器确认当前在评论页")
            self.current_page = "comments"
            return "comments"
        
        # 检查是否在作者主页
        author_indicators = self.element_manager.find_page_indicators('author', min_matches=1)
        if author_indicators:
            logger.info("通过页面指示器确认当前在作者主页")
            self.current_page = "author"
            return "author"
            
        # 检查是否在消息页
        message_indicators = self.element_manager.find_page_indicators('message', min_matches=1)
        if message_indicators:
            logger.info("通过页面指示器确认当前在消息页")
            self.current_page = "message"
            return "message"
            
        # 检查是否在热门页
        hot_indicators = self.element_manager.find_page_indicators('hot', min_matches=1)
        if hot_indicators:
            logger.info("通过页面指示器确认当前在热门页")
            self.current_page = "hot"
            return "hot"
            
        # 检查是否在发布页
        publish_indicators = self.element_manager.find_page_indicators('publish', min_matches=1)
        if publish_indicators:
            logger.info("通过页面指示器确认当前在发布页")
            self.current_page = "publish"
            return "publish"
        
        # 未能识别的页面
        logger.warning("无法通过页面指示器识别当前页面类型")
        self.current_page = "unknown"
        return "unknown"
    
    def go_to_home(self, return_to_search: bool = False) -> bool:
        """
        回到首页或搜索页
        
        Args:
            return_to_search: 是否只需回到搜索页而不是首页
        
        Returns:
            是否成功
        """
        # 检测当前页面
        current_page = self.detect_current_page()
        
        # 如果希望回到搜索页，且当前已在搜索页，直接返回成功
        if return_to_search and current_page == "search":
            logger.info("当前已在搜索页")
            return True
        
        # 如果希望回到搜索结果页，且当前已在搜索结果页，直接返回成功
        if return_to_search and current_page == "search_results":
            logger.info("当前已在搜索结果页")
            return True
            
        # 如果需要回到首页，且当前已在首页，直接返回成功
        if not return_to_search and current_page == "home":
            logger.info("当前已在首页")
            return True
        
        logger.info(f"当前在{current_page}页面，尝试返回" + ("搜索页/搜索结果页" if return_to_search else "首页"))
        
        # 根据当前页面类型选择不同的返回策略
        if current_page == "detail":
            # 在笔记详情页，按1次返回键
            logger.info("详情页：按1次返回键")
            self.back()
            time.sleep(3)
        elif current_page == "profile":
            # 在用户个人页，按1次返回键
            logger.info("用户个人页：按1次返回键")
            self.back()
            time.sleep(2)
        elif current_page == "search":
            # 如果当前在搜索页，根据需求决定操作
            if not return_to_search:
                # 需要回到首页，按2次返回键
                logger.info("搜索页：按2次返回键回到首页")
                for _ in range(2):
                    self.back()
                    time.sleep(1)
            # 如果需要留在搜索页，不做任何操作
        elif current_page == "search_results":
            # 如果当前在搜索结果页，根据需求决定操作
            if not return_to_search:
                # 需要回到首页，按2次返回键
                logger.info("搜索结果页：按2次返回键回到首页")
                for _ in range(2):
                    self.back()
                    time.sleep(1)
            # 如果需要留在搜索结果页，不做任何操作  
        elif current_page == "author":
            # 在作者主页，按2次返回键
            logger.info("作者主页：按2次返回键")
            for _ in range(2):
                self.back()
                time.sleep(1)
        elif current_page == "comments":
            # 在评论页，按2次返回键
            logger.info("评论页：按2次返回键")
            for _ in range(2):
                self.back()
                time.sleep(1)
        elif current_page == "message" or current_page == "hot" or current_page == "publish":
            # 在消息页/热门页/发布页，按1次返回键
            logger.info(f"{current_page}页：按1次返回键")
            self.back()
            time.sleep(2)
        elif current_page == "discovery":
            # 在发现视频页，按1次返回键应当回到搜索结果页
            logger.info("发现视频页：按1次返回键")
            self.back()
            time.sleep(2)
            
            # 验证是否回到搜索结果页
            if self.verify_page_type("search_results"):
                logger.info("✅ 成功从发现视频页返回搜索结果页")
            else:
                current_page = self.detect_current_page()
                logger.warning(f"⚠️ 从发现视频页返回后当前页面为: {current_page}，不是预期的搜索结果页")
                
                # 如果回到了普通搜索页，可能需要重新执行搜索
                if current_page == "search" and not return_to_search:
                    logger.warning("⚠️ 从发现视频页返回到了搜索页，而不是搜索结果页，需要额外处理")
        elif current_page == "unknown":
            # 未能识别的页面，重启应用
            logger.info("无法识别当前页面，尝试重启应用")
            return self.restart_app()
        else:
            # 其他页面先尝试1次返回
            logger.info(f"其他页面({current_page})：尝试返回")
            self.back()
            time.sleep(2)
        
        # 检查是否已回到目标页面
        time.sleep(1)
        if return_to_search:
            # 检查是否回到搜索页或搜索结果页
            current_page = self.detect_current_page()
            if current_page == "search" or current_page == "search_results":
                logger.info(f"已成功返回{current_page}")
                return True
            # 如果返回后不在搜索页但在首页，可能需要执行搜索
            elif current_page == "home":
                logger.info("已回到首页，但目标是搜索页/搜索结果页")
                return False
        else:
            # 检查是否回到首页
            current_page = self.detect_current_page(hint="home")
            if current_page == "home":
                logger.info("已成功返回首页")
                return True
        
        # 如果返回键策略失败，再尝试点击相应的底部导航按钮
        if not return_to_search:
            logger.info("返回键策略未能回到首页，尝试点击首页按钮")
            if self.click_element("home_tab", timeout=3):
                time.sleep(2)
                if self.detect_current_page(hint="home") == "home":
                    logger.info("点击首页按钮成功返回首页")
                    return True
        
        # 如果回到搜索页失败，或点击首页按钮也失败，尝试重启应用
        if not return_to_search:
            logger.info("所有常规导航方法失败，尝试重启应用")
            if self.restart_app():
                time.sleep(5)
                if self.detect_current_page(hint="home") == "home":
                    logger.info("重启应用后成功回到首页")
                    return True
        
        logger.error(f"无法返回" + ("搜索页/搜索结果页" if return_to_search else "首页"))
        return False
    
    def open_app(self) -> bool:
        """
        打开小红书应用
        
        Returns:
            是否成功
        """
        # 保存当前关键词状态
        saved_search_keywords = getattr(self, 'search_keywords', None)
        saved_target = getattr(self, 'last_search_keyword', None)
        
        # 直接使用device打开应用
        if not self.device:
            logger.error("设备对象未初始化")
            return False

        result = self.device.open_app(self.PACKAGE_NAME)
        self.record_action("打开小红书应用", result)
        
        if result:
            # 等待应用启动
            time.sleep(5)
            
            # 恢复保存的状态
            if saved_search_keywords:
                self.search_keywords = saved_search_keywords
                logger.info(f"应用打开后恢复搜索关键词: {saved_search_keywords}")
            
            if saved_target:
                self.last_search_keyword = saved_target
                logger.info(f"应用打开后恢复最后搜索关键词: {saved_target}")
            
            # 检查是否成功进入首页
            if self.detect_current_page() == "home":
                return True
            else:
                # 尝试回到首页
                return self.go_to_home()
        
        return False
    
    def search_keyword(self, keyword: Optional[str] = None) -> bool:
        """
        搜索关键词
        
        Args:
            keyword: 搜索关键词，如果不提供则使用随机关键词
            
        Returns:
            是否成功
        """
        # 确保在首页或搜索页
        current_page = self.detect_current_page()
        if current_page not in ["home", "search"]:
            if not self.go_to_home():
                return False
        
        # 准备搜索关键词
        if keyword is None:
            keyword = self._get_random_keyword()
            if keyword is None:
                logger.error("未提供搜索关键词且随机关键词功能已禁用")
                return False
        
        # 保存最后一次搜索的关键词
        self.last_search_keyword = keyword
        
        try:
            # 如果在首页，点击搜索按钮
            if current_page == "home":
                if not self.click_element("search_button"):
                    return False
                
                # 等待进入搜索页
                time.sleep(2)
            
            # 点击搜索输入框
            if not self.click_element("search_input"):
                return False
            
            # 等待输入法弹出
            time.sleep(2)
            
            # 输入搜索关键词
            if not self.input_text(keyword):
                return False
            
            # 点击搜索按钮
            if self.click_element("search_confirm_button"):
                # 搜索成功
                self.search_count += 1
                
                # 等待搜索结果加载
                time.sleep(5)
                
                # 验证当前是否在搜索结果页
                current_page = self.detect_current_page()
                if current_page == "search_results":
                    logger.info(f"✅ 搜索关键词 '{keyword}' 成功，已进入搜索结果页")
                    return True
                elif current_page == "search":
                    logger.warning(f"⚠️ 搜索关键词 '{keyword}' 后仍在搜索页，而非搜索结果页")
                    # 尝试再次点击搜索按钮
                    if self.click_element("search_confirm_button"):
                        time.sleep(3)
                        if self.detect_current_page() == "search_results":
                            logger.info(f"✅ 二次尝试搜索关键词 '{keyword}' 成功，已进入搜索结果页")
                            return True
                        else:
                            logger.error(f"❌ 二次尝试搜索关键词 '{keyword}' 失败，未能进入搜索结果页")
                            return False
                    return False
                else:
                    logger.warning(f"⚠️ 搜索关键词 '{keyword}' 后进入了未预期的页面: {current_page}")
                    return False
            
            return False
                
        except Exception as e:
            logger.error(f"搜索异常: {str(e)}")
            self.record_action("搜索", False)
            return False
    
    def search_user(self, username: str) -> bool:
        """
        搜索用户
        
        Args:
            username: 用户名
            
        Returns:
            是否成功
        """
        # 确保在首页或搜索页
        current_page = self.detect_current_page()
        if current_page not in ["home", "search"]:
            if not self.go_to_home():
                return False
        
        try:
            # 如果在首页，点击搜索按钮
            if current_page == "home":
                if not self.click_element("search_button"):
                    return False
                
                # 等待进入搜索页
                time.sleep(2)
            
            # 点击搜索输入框
            if not self.click_element("search_input"):
                return False
            
            # 等待输入法弹出
            time.sleep(1)
            
            # 输入用户名
            if not self.input_text(username):
                return False
            
            # 点击搜索按钮
            if not self.click_element("search_confirm_button"):
                return False
            
            # 等待搜索结果加载
            time.sleep(3)
            
            # 切换到用户标签
            if not self.click_element("user_tab"):
                return False
            
            # 等待用户列表加载
            time.sleep(2)
            
            # 点击第一个用户结果
            if self.click_element("first_user_result"):
                # 等待用户页面加载
                time.sleep(3)
                
                # 检查是否成功进入用户页面
                return self.detect_current_page() == "profile"
            
            return False
                
        except Exception as e:
            logger.error(f"搜索用户异常: {str(e)}")
            self.record_action("搜索用户", False)
            return False
    
    def enter_detail_page(self) -> bool:
        """
        进入笔记详情页
        
        Returns:
            是否成功
        """
        # 当前页面类型
        current_page = self.detect_current_page(hint="detail")
        
        # 如果已在详情页，直接返回成功
        if current_page == "detail":
            return True
        
        # 只有在首页或搜索结果页才能点击进入详情页，普通搜索页没有可点击的内容
        if current_page in ["home", "search_results"]:
            # 获取屏幕尺寸
            if not self.device:
                logger.error("设备对象未初始化")
                return False

            if hasattr(self.device, 'get_screen_size'):
                width, height = self.device.get_screen_size()
            else:
                # 使用默认屏幕尺寸
                width, height = self.DEFAULT_SCREEN_WIDTH, self.DEFAULT_SCREEN_HEIGHT
                logger.warning(f"设备不支持get_screen_size方法，使用默认尺寸: {width}x{height}")
            
            # 点击屏幕上的随机位置（避开顶部和底部的导航栏区域）
            x = random.randint(int(width * 0.1), int(width * 0.9))
            y = random.randint(int(height * 0.3), int(height * 0.7))
            
            try:
                # 模拟点击
                self.device.tap(x, y)  # 使用tap方法代替click
                self.record_action(f"点击屏幕位置: ({x}, {y})")
                
                # 等待页面加载
                time.sleep(3)
                
                # 先检查是否进入详情页
                if self.verify_page_type("detail"):
                    logger.info("✅ 成功进入详情页")
                    return True
                    
                # 再检查是否是发现视频页
                if self.verify_page_type("discovery"):
                    # 检测到是发现视频页，处理方式：等待一会后返回搜索结果页
                    wait_time = random.uniform(1.0, 2.0)
                    logger.info(f"⚠️ 检测到在发现视频页，等待 {wait_time:.1f} 秒后返回")
                    time.sleep(wait_time)
                    
                    # 按返回键回到搜索结果页
                    logger.info("按返回键回到搜索结果页")
                    self.back()
                    time.sleep(2)
                    
                    # 验证是否回到搜索结果页，不再接受普通搜索页
                    if self.verify_page_type("search_results"):
                        logger.info("✅ 成功从发现视频页返回搜索结果页")
                    else:
                        current_page = self.detect_current_page()
                        logger.warning(f"⚠️ 返回后页面类型为: {current_page}，不是预期的搜索结果页")
                    
                    return False
                
                # 如果既不是详情页也不是发现视频页，可能进入了其他页面
                current_page = self.detect_current_page()
                logger.warning(f"⚠️ 进入了其他页面: {current_page}，尝试返回")
                self.back()
                time.sleep(2)
                return False
                
            except Exception as e:
                logger.error(f"点击进入详情页异常: {str(e)}")
                self.record_action("点击进入详情页", False)
                return False
        elif current_page == "search":
            # 如果当前在搜索页，提示错误并返回失败
            logger.warning("⚠️ 当前在搜索页，无法点击内容进入详情页，需要先执行搜索获得搜索结果")
            return False
        # 如果在用户个人页，点击第一篇笔记
        elif current_page == "profile":
            if self.click_element("first_post"):
                # 等待页面加载
                time.sleep(3)
                
                # 先检查是否进入详情页
                if self.verify_page_type("detail"):
                    return True
                    
                # 处理可能进入发现视频页的情况
                if self.verify_page_type("discovery"):
                    # 检测到是发现视频页，处理方式：等待几秒后返回
                    wait_time = random.uniform(1.0, 2.0)
                    logger.info(f"⚠️ 检测到进入发现视频页而非详情页，等待 {wait_time:.1f} 秒后返回")
                    time.sleep(wait_time)
                    self.back()
                    time.sleep(2)
                    return False
                
                # 检查是否进入了其他页面
                current_page = self.detect_current_page()
                if current_page != "detail":
                    logger.warning(f"⚠️ 进入了其他页面: {current_page}，尝试返回")
                    self.back()
                    time.sleep(2)
                    return False
                
                return True
        
        return False
    
    def enter_comments_page(self) -> bool:
        """
        进入评论页面
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            if not self.enter_detail_page():
                return False
        
        # 点击评论按钮
        if self.click_element("comment_button"):
            # 等待评论页面加载
            time.sleep(2)
            
            # 检查是否进入评论页面
            return self.detect_current_page(hint="comments") == "comments"
        
        return False
    
    def like_post(self) -> bool:
        """
        点赞笔记
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            if not self.enter_detail_page():
                return False
        
        # 点击点赞按钮
        result = self.click_element("like_button")
        if result:
            self.like_count += 1
        
        return result
    
    def collect_post(self) -> bool:
        """
        收藏笔记
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            if not self.enter_detail_page():
                return False
        
        # 点击收藏按钮
        result = self.click_element("collect_button")
        if result:
            self.collect_count += 1
            # 等待收藏完成
            time.sleep(1)
        
        return result
    
    def follow_user(self) -> bool:
        """
        关注用户
        
        Returns:
            是否成功
        """
        # 检查当前页面
        current_page = self.detect_current_page()
        
        if current_page == "profile":
            # 如果在用户个人页，直接点击关注按钮
            result = self.click_element("follow_button")
            if result:
                self.follow_count += 1
                # 等待关注完成
                time.sleep(1)
            
            return result
            
        elif current_page == "detail":
            # 直接在详情页点击关注按钮
            result = self.click_element("follow_button")
            if result:
                self.follow_count += 1
                # 等待关注完成
                time.sleep(1)
                
            return result
        
        return False
    
    def comment_post(self, comment_text: Optional[str] = None, return_after_comment: bool = True, comment_mode: str = "text", sticker_id: Optional[int] = None) -> bool:
        """
        评论笔记
        
        Args:
            comment_text: 评论内容，如果不提供则使用随机评论
            return_after_comment: 评论成功后是否自动返回，默认为True
            comment_mode: "text" | "sticker" | "auto"
            sticker_id: 指定表情包id（可选）
            
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            logger.warning("当前不在详情页，无法进行评论")
            return False
        
        # 准备评论文本
        # 在随机模式且没有评论内容时自动切换为表情包评论
        if comment_mode == "auto":
            # 如果没有文本评论或获取随机评论失败，自动切换为表情包模式
            if comment_text is None:
                comment_text = self._get_random_comment()
                
            # 检查是否有评论文本
            if comment_text is None:
                logger.info("随机模式下无文本评论，自动切换为表情包评论")
                comment_mode = "sticker"  # 自动切换为表情包评论
                
        # 表情包评论逻辑
        if comment_mode == "sticker":
            try:
                # === 新增：表情评论前先进行5次向下滑动 ===
                logger.info("📝 表情评论前先进行5次向下滑动浏览")
                self._perform_pre_comment_swipes()

                # 点击评论按钮
                if not self.click_element("comment_button"):
                    logger.warning("点击评论按钮失败")
                    return False

                # 等待评论框出现
                time.sleep(2.5)  # 增加等待时间
                
                # 点击评论输入框
                if not self.click_element("comment_input"):
                    logger.warning("点击评论输入框失败")
                    if return_after_comment:
                        self.back()
                    return False
                
                time.sleep(2)  # 增加等待时间
                
                # 1. 点击表情按钮（打开表情面板）
                retry_count = 0
                while retry_count < 3:  # 最多重试3次
                    if self.click_element("sticker_panel_button"):
                        break
                    logger.warning(f"点击表情按钮失败，重试第{retry_count + 1}次")
                    time.sleep(1)
                    retry_count += 1
                
                if retry_count >= 3:
                    logger.warning("点击表情按钮失败，超过最大重试次数")
                    if return_after_comment:
                        self.back()
                    return False
                
                time.sleep(2)  # 增加等待时间
                
                # 2. 点击表情包按钮（切换到表情包分组/标签页）
                retry_count = 0
                while retry_count < 3:  # 最多重试3次
                    if self.click_element("sticker_tab_button"):
                        break
                    logger.warning(f"点击表情包按钮失败，重试第{retry_count + 1}次")
                    time.sleep(1)
                    retry_count += 1
                
                if retry_count >= 3:
                    logger.warning("点击表情包按钮失败，超过最大重试次数")
                    if return_after_comment:
                        self.back()
                    return False
                
                time.sleep(1.5)  # 增加等待时间，确保表情包面板完全加载
                
                # 3. 选择表情包
                sticker = None
                if sticker_id is not None:
                    sticker = next((s for s in self.sticker_templates if s.get("id") == sticker_id), None)
                if not sticker:
                    if not self.sticker_templates:
                        logger.warning("表情包模板为空，无法评论")
                        if return_after_comment:
                            self.back()
                        return False
                    sticker = random.choice(self.sticker_templates)
                
                logger.info(f"准备点击表情包: {sticker.get('element')}")  # 添加日志
                sticker_element = sticker.get("element")
                
                # 尝试点击表情包，如果失败则重试
                retry_count = 0
                while retry_count < 2:  # 最多重试2次
                    if self.click_element(sticker_element):
                        break
                    logger.warning(f"点击表情包{sticker_element}失败，重试第{retry_count + 1}次")
                    time.sleep(1.5)  # 重试前等待
                    retry_count += 1
                
                if retry_count >= 2:
                    logger.warning(f"点击表情包{sticker_element}失败，超过最大重试次数")
                    if return_after_comment:
                        self.back()
                    return False
                
                time.sleep(1)  # 增加等待时间
                
                # 4. 点击发送按钮
                retry_count = 0
                while retry_count < 2:  # 最多重试2次
                    if self.click_element("comment_send_button"):
                        break
                    logger.warning(f"点击发送按钮失败，重试第{retry_count + 1}次")
                    time.sleep(1)
                    retry_count += 1
                
                if retry_count >= 2:
                    logger.warning("点击发送按钮失败，超过最大重试次数")
                    if return_after_comment:
                        self.back()
                    return False
                
                # 评论成功
                self.comment_count += 1
                
                # 等待评论发送完成
                time.sleep(2)  # 增加等待时间

                # === 新增追评逻辑 (仅在表情评论模式下) ===
                if comment_mode == "sticker" and self.enable_follow_up and self.follow_up_templates:
                    # 随机选择追评内容 (使用独立的追评文本模板)
                    follow_up_text = random.choice(self.follow_up_templates)
                    logger.info(f"准备执行追评操作，内容: {follow_up_text[:20]}...")

                    # 记录追评开始时间
                    follow_up_start = time.time()

                    # 执行追评
                    follow_up_success = self._execute_follow_up_comment(follow_up_text, sticker_id)

                    # 记录追评操作耗时
                    self.follow_up_duration = time.time() - follow_up_start

                    # 累积到总追评时间中（用于关键词时长补偿）
                    self.total_follow_up_time += self.follow_up_duration

                    if follow_up_success:
                        logger.info(f"✅ 追评操作成功，耗时 {self.follow_up_duration:.1f} 秒（累积追评时间: {self.total_follow_up_time:.1f} 秒）")
                        self.comment_count += 1  # 追评也算作一次评论
                    else:
                        logger.warning(f"⚠️ 追评操作失败，耗时 {self.follow_up_duration:.1f} 秒（累积追评时间: {self.total_follow_up_time:.1f} 秒）")

                # 返回搜索结果页
                if return_after_comment:
                    self.back()

                    # 验证是否返回到搜索结果页
                    time.sleep(1.5)  # 增加等待时间
                    if self.verify_page_type("search_results"):
                        logger.info("✅ 表情包评论成功并返回搜索结果页")
                    else:
                        current_page = self.detect_current_page()
                        logger.warning(f"⚠️ 表情包评论后返回可能未到达搜索结果页，当前页面：{current_page}")
                else:
                    logger.info("✅ 表情包评论成功")

                return True
                    
            except Exception as e:
                logger.error(f"表情包评论异常: {str(e)}")
                logger.error(traceback.format_exc())  # 添加堆栈跟踪
                self.record_action("表情包评论", False)
                
                # 尝试返回搜索结果页
                if return_after_comment:
                    self.back()
                    logger.info("遇到异常，返回搜索结果页")
                
                return False
        else:
            # 文本评论逻辑
            if comment_text is None:
                comment_text = self._get_random_comment()
            
            # 如果没有评论内容，无法评论
            if comment_text is None:
                logger.warning("⚠️ 无法评论：评论内容为空")
                return False
            
            try:
                # 点击评论按钮
                if not self.click_element("comment_button"):
                    logger.warning("点击评论按钮失败")
                    return False
                
                # 等待评论框出现
                time.sleep(2)
                
                # 点击评论输入框
                if not self.click_element("comment_input"):
                    logger.warning("点击评论输入框失败")
                    # 返回搜索结果页
                    if return_after_comment:
                        self.back()
                        logger.info("返回搜索结果页")
                    return False
                
                # 等待输入法弹出
                time.sleep(1)
                
                # 输入评论内容
                if not self.input_text(comment_text):
                    logger.warning("输入评论内容失败")
                    # 返回搜索结果页
                    if return_after_comment:
                        self.back()
                        logger.info("返回搜索结果页")
                    return False
                
                # 点击发送按钮
                if self.click_element("comment_send_button"):
                    # 评论成功
                    self.comment_count += 1
                    
                    # 等待评论发送完成
                    time.sleep(2)
                    
                    # 返回搜索结果页
                    if return_after_comment:
                        self.back()
                        
                        # 验证是否返回到搜索结果页
                        time.sleep(2)
                        if self.verify_page_type("search_results"):
                            logger.info("✅ 评论成功并返回搜索结果页")
                        else:
                            current_page = self.detect_current_page()
                            logger.warning(f"⚠️ 评论后返回可能未到达搜索结果页，当前页面：{current_page}")
                    else:
                        logger.info("✅ 评论成功")
                    
                    return True
                else:
                    logger.warning("点击发送按钮失败")
                    # 返回搜索结果页
                    if return_after_comment:
                        self.back()
                        logger.info("返回搜索结果页")
                    return False
                    
            except Exception as e:
                logger.error(f"评论异常: {str(e)}")
                self.record_action("评论", False)
                
                # 尝试返回搜索结果页
                if return_after_comment:
                    self.back()
                    logger.info("遇到异常，返回搜索结果页")
                
                return False

    def _execute_follow_up_comment(self, follow_up_text: str, _sticker_id: Optional[int] = None) -> bool:
        """
        执行追评操作的核心方法

        Args:
            follow_up_text: 追评文本内容
            sticker_id: 表情包ID（可选）

        Returns:
            bool: 是否成功完成追评
        """
        try:
            logger.info(f"开始执行追评操作，文本内容: {follow_up_text[:20]}...")

            # 步骤1-2: 等待-点击评论按钮（最简化流程）
            if not self._navigate_to_comment_section():
                logger.warning("导航到评论区失败")
                return False

            # 步骤3: 验证进入评论区并验证reply_button_1
            time.sleep(3)
            if not self._verify_comment_section_and_reply_button():
                logger.warning("未能进入评论区或reply_button_1不存在")
                return False

            # 步骤4-8: 执行文本追评
            if not self._send_follow_up_text(follow_up_text):
                logger.warning("发送追评文本失败")
                return False

            logger.info("✅ 追评操作成功完成")
            return True

        except Exception as e:
            logger.error(f"追评操作异常: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _navigate_to_comment_section(self) -> bool:
        """导航到评论区（最简化流程：等待-点击评论按钮）"""
        try:
            logger.info("📝 开始导航到评论区")

            # 步骤1: 等待1.5秒
            logger.info("📝 等待1.5秒")
            time.sleep(1.5)

            # 步骤2: 点击评论按钮
            logger.info("📝 点击评论按钮")
            if not self.click_element("comment_button"):
                logger.warning("点击评论按钮失败")
                return False

            logger.info("✅ 成功导航到评论区")
            return True

        except Exception as e:
            logger.error(f"导航到评论区异常: {str(e)}")
            return False

    def _verify_comment_section_and_reply_button(self) -> bool:
        """验证是否滑动到评论区（通过检测reply_button_1图像）"""
        try:
            # 直接检测reply_button_1图像，存在即表示已滑动到评论区
            if self.element_manager and self.element_manager.find_element("reply_button_1"):
                logger.info("✅ 检测到reply_button_1，已成功滑动到评论区")
                return True
            else:
                logger.warning("⚠️ 未检测到reply_button_1，可能未滑动到评论区")
                return False
        except Exception as e:
            logger.error(f"验证评论区异常: {str(e)}")
            return False

    def _send_follow_up_text(self, follow_up_text: str) -> bool:
        """发送追评文本"""
        try:
            # 4. 图像定位'reply_button_1'，选择Y坐标最小的(最上方)匹配定位
            # 强制使用最上方选择逻辑，不依赖优化定位器
            logger.info("🎯 开始查找最上方的reply_button_1")

            # 直接使用element_manager的find_topmost_element_by_template方法
            if self.element_manager and hasattr(self.element_manager, 'find_topmost_element_by_template'):
                topmost_reply_button = self.element_manager.find_topmost_element_by_template("reply_button_1")
                if topmost_reply_button:
                    # 点击最上方的reply_button_1
                    logger.info(f"✅ 找到最上方的reply_button_1: ({topmost_reply_button.get('x', 0)}, {topmost_reply_button.get('y', 0)})")
                    if hasattr(self.element_manager, 'click_coordinates'):
                        if self.element_manager.click_coordinates(topmost_reply_button, "最上方的reply_button_1", no_offset=True):
                            logger.info("✅ 成功点击最上方的reply_button_1")
                        else:
                            logger.warning("点击最上方reply_button_1失败，回退到标准方法")
                            if not self.click_element("reply_button_1"):
                                logger.warning("点击reply_button_1失败")
                                return False
                    else:
                        # 如果没有click_coordinates方法，使用设备直接点击
                        x = topmost_reply_button.get('x', 0)
                        y = topmost_reply_button.get('y', 0)

                        # 转换numpy类型为Python原生类型
                        if hasattr(x, 'item'):
                            x = x.item()
                        if hasattr(y, 'item'):
                            y = y.item()

                        if x > 0 and y > 0:
                            logger.info(f"使用设备直接点击最上方reply_button_1: ({x}, {y})")
                            # 优先使用click方法，回退到tap方法
                            click_success = False
                            if hasattr(self.device, 'click'):
                                click_success = self.device.click(x, y)
                                if click_success:
                                    logger.info("✅ 设备click方法点击最上方reply_button_1成功")
                            elif hasattr(self.device, 'tap'):
                                click_success = self.device.tap(x, y)
                                if click_success:
                                    logger.info("✅ 设备tap方法点击最上方reply_button_1成功")

                            if not click_success:
                                logger.warning("设备直接点击失败，回退到标准方法")
                                if not self.click_element("reply_button_1"):
                                    logger.warning("点击reply_button_1失败")
                                    return False
                        else:
                            logger.warning("无效坐标，回退到标准方法")
                            if not self.click_element("reply_button_1"):
                                logger.warning("点击reply_button_1失败")
                                return False
                else:
                    logger.warning("未找到最上方的reply_button_1，使用标准方法")
                    if not self.click_element("reply_button_1"):
                        logger.warning("点击reply_button_1失败")
                        return False
            else:
                # element_manager不支持find_topmost_element_by_template，使用标准方法
                logger.warning("ElementManager不支持find_topmost_element_by_template方法，使用标准方法")
                if not self.click_element("reply_button_1"):
                    logger.warning("点击reply_button_1失败")
                    return False
            time.sleep(2)  # 增加等待时间，确保页面稳定

            # 5. 图像定位'reply_button_2'，进行点击
            if not self.click_element("reply_button_2"):
                logger.warning("点击reply_button_2失败")
                return False
            time.sleep(2)  # 增加等待时间，确保输入框加载完成

            # 6. 输入追评文本
            if not self.input_text(follow_up_text):
                logger.warning("输入追评文本失败")
                return False
            time.sleep(1)

            # 7. 点击发送按钮
            if not self.click_element("comment_send_button"):
                logger.warning("点击发送按钮失败")
                return False
            time.sleep(2)

            logger.info("✅ 追评文本发送成功")
            return True

        except Exception as e:
            logger.error(f"发送追评文本异常: {str(e)}")
            return False

    def _perform_pre_comment_swipes(self) -> None:
        """表情评论前执行5次向下滑动，模拟自然浏览行为"""
        try:
            logger.info("📝 开始执行表情评论前的5次向下滑动")

            # 获取屏幕尺寸
            width = self.DEFAULT_SCREEN_WIDTH
            height = self.DEFAULT_SCREEN_HEIGHT

            # 执行5次向下滑动
            for i in range(5):
                # 检查是否请求停止
                if self.check_stop_requested():
                    logger.info("⚠️ 检测到停止请求，中断滑动操作")
                    break

                # 参考详情页浏览的贝塞尔曲线滑动逻辑
                # 向下滑动：从上向下滑
                start_y = int(height * 0.3)  # 从屏幕30%位置开始
                end_y = int(height * 0.7)    # 滑动到屏幕70%位置

                # 1. 不使用固定的屏幕中心点作为起点
                start_x = random.randint(int(width * 0.3), int(width * 0.7))

                # 2. 终点添加轻微随机偏移
                end_x = start_x + random.randint(-30, 30)

                # 3. 确保坐标在屏幕安全范围内
                end_x = max(50, min(width - 50, end_x))

                # 4. 添加持续时间随机性
                duration = random.randint(800, 1500)
                duration_sec = duration / 1000.0
                natural_duration = duration_sec * random.uniform(0.9, 1.1)

                # 5. 贝塞尔曲线的控制点随机性
                bezier_start_x = start_x + random.randint(-15, 15)
                bezier_end_x = end_x + random.randint(-15, 15)

                # 6. 确保坐标在屏幕范围内
                bezier_start_x = max(50, min(width - 50, bezier_start_x))
                bezier_end_x = max(50, min(width - 50, bezier_end_x))

                logger.debug(f"表情评论前向下滑动({i+1}/5): ({bezier_start_x}, {start_y}) -> ({bezier_end_x}, {end_y}), 时长: {natural_duration:.2f}秒")

                try:
                    # 先尝试贝塞尔曲线滑动
                    success = self.swipe_with_bezier(bezier_start_x, start_y, bezier_end_x, end_y, natural_duration)

                    if not success:
                        logger.warning("⚠️ 贝塞尔曲线滑动失败，尝试普通滑动")
                        success = self.swipe(start_x, start_y, end_x, end_y, duration)

                except Exception as e:
                    logger.warning(f"⚠️ 高级滑动异常: {str(e)}，尝试普通滑动")
                    try:
                        success = self.swipe(start_x, start_y, end_x, end_y, duration)
                    except Exception as e2:
                        logger.error(f"❌ 普通滑动也失败: {str(e2)}")
                        success = False

                if success:
                    logger.info(f"✅ 表情评论前成功向下滑动 ({i+1}/5)")
                else:
                    logger.warning(f"⚠️ 表情评论前向下滑动失败 ({i+1}/5)")

                # 每次滑动后短暂等待
                wait_time = random.uniform(0.8, 1.5)
                time.sleep(wait_time)

            logger.info("✅ 表情评论前的5次向下滑动完成")

        except Exception as e:
            logger.error(f"表情评论前滑动异常: {str(e)}")
            logger.error(traceback.format_exc())

    def interact_with_post(self,
                         like_probability: float = 0.7,
                         collect_probability: float = 0.3,
                         follow_probability: float = 0.1,
                         enable_comment: bool = True,
                         comment_text: Optional[str] = None,
                         comment_mode: str = "text",
                         sticker_id: Optional[int] = None) -> Dict[str, bool]:
        """
        与当前笔记互动，根据概率执行不同操作
        
        Args:
            like_probability: 点赞概率
            collect_probability: 收藏概率
            follow_probability: 关注概率
            enable_comment: 是否启用评论功能
            comment_text: 评论内容，如果不提供则使用随机评论
            comment_mode: 评论类型 "text" | "sticker" | "auto"
            sticker_id: 表情包id
            
        Returns:
            互动结果
        """
        # 确保在详情页，使用hint参数优先检查详情页
        if self.detect_current_page(hint="detail") != "detail":
            logger.warning("⚠️ 当前不在详情页，无法进行互动")
            return {
                "like": False,
                "collect": False,
                "comment": False,
                "follow": False
            }
        
        result = {
            "like": False,
            "collect": False,
            "comment": False,
            "follow": False
        }
        
        # 随机决定是否执行各操作
        like = random.random() < like_probability
        collect = random.random() < collect_probability
        follow = random.random() < follow_probability
        
        # 记录计划的互动操作
        planned_actions = []
        if like:
            planned_actions.append("点赞")
        if collect:
            planned_actions.append("收藏")
        if enable_comment:
            planned_actions.append("评论")
        if follow:
            planned_actions.append("关注")
            
        if planned_actions:
            logger.info(f"📝 详情页计划互动操作: {', '.join(planned_actions)}")
        else:
            logger.info("📝 详情页未计划任何互动操作")
        
        # 执行互动操作
        if like:
            logger.info("📝 尝试点赞笔记")
            result["like"] = self.like_post()
            if result["like"]:
                logger.info("✅ 点赞成功")
            else:
                logger.warning("⚠️ 点赞失败")
            # 等待一下再进行下一个操作
            self.random_wait(1.0, 2.0)
        
        if collect and not self.check_stop_requested():
            logger.info("📝 尝试收藏笔记")
            result["collect"] = self.collect_post()
            if result["collect"]:
                logger.info("✅ 收藏成功")
            else:
                logger.warning("⚠️ 收藏失败")
            self.random_wait(1.0, 2.0)
        
        if enable_comment and not self.check_stop_requested():
            logger.info("📝 尝试评论笔记")
            
            # 修复随机评论模式 - 参照AccountNurturingController的修复方式
            if comment_mode == "auto":
                # 先选择评论类型，确保日志清晰记录选择结果
                chosen_mode = random.choice(["text", "sticker"])
                logger.info(f"随机评论模式已选择: {chosen_mode}评论")
                # 如果选择了文本评论，确保有评论文本
                if chosen_mode == "text":
                    comment_text = self._get_random_comment()
                    if comment_text is None:
                        logger.warning("文本评论内容为空，自动切换到表情包评论")
                        chosen_mode = "sticker"
                # 使用最终决定的评论模式
                result["comment"] = self.comment_post(comment_text if chosen_mode == "text" else None, 
                                                    return_after_comment=False, 
                                                    comment_mode=chosen_mode, 
                                                    sticker_id=sticker_id)
            else:
                # 原有的文本评论或表情包评论逻辑
                if comment_mode == "text" and comment_text is None:
                    comment_text = self._get_random_comment()
                if (comment_mode == "text" and comment_text is not None) or comment_mode in ["sticker"]:
                    if comment_mode == "text" and comment_text is not None:
                        logger.info(f"📝 使用评论内容: \"{comment_text[:15]}...\"")
                    elif comment_mode == "sticker":
                        logger.info(f"📝 使用表情包评论")
                    # 修改评论调用，设置不在评论方法内返回
                    result["comment"] = self.comment_post(comment_text, return_after_comment=False, comment_mode=comment_mode, sticker_id=sticker_id)
                else:
                    logger.warning("⚠️ 无法评论：评论内容为空")
                    result["comment"] = False
            
            if result["comment"]:
                logger.info("✅ 评论成功")
            else:
                logger.warning("⚠️ 评论失败")
            self.random_wait(1.0, 2.0)
        
        if follow and not self.check_stop_requested():
            logger.info("📝 尝试关注作者")
            result["follow"] = self.follow_user()
            if result["follow"]:
                logger.info("✅ 关注成功")
            else:
                logger.warning("⚠️ 关注失败")
            self.random_wait(1.0, 2.0)
        
        # 总结互动结果
        success_actions = [action for action, success in result.items() if success]
        if success_actions:
            logger.info(f"✅ 互动总结: 成功完成 {len(success_actions)}/{len(planned_actions)} 个互动操作")
        
        # 互动完成后统一返回
        if enable_comment and result["comment"]:
            # 如果评论成功了，需要手动返回
            self.back()
            time.sleep(2)
            if self.verify_page_type("search_results"):
                logger.info("✅ 互动后成功返回搜索结果页")
            else:
                current_page = self.detect_current_page()
                logger.warning(f"⚠️ 互动后返回可能未到达搜索结果页，当前页面：{current_page}")
        
        return result

    def interact_with_posts(self, 
                          target_type: str = "keyword",
                          target: Optional[str] = None,
                          max_targets: int = 5,
                          like_probability: float = 0.7, 
                          collect_probability: float = 0.3,
                          comment_probability: float = 0.5,  # 默认总是评论
                          follow_probability: float = 0.1,
                          comments: Optional[List[str]] = None,
                          duration_minutes: float = 30.0,
                          min_view_time: float = 6.0,  # 最小浏览时间
                          max_view_time: float = 13.0,  # 最大浏览时间
                          stay_in_search: bool = True,  # 是否停留在搜索页，不回首页
                          comment_mode: str = "text",    # 评论类型
                          sticker_id: Optional[int] = None,  # 指定表情包id
                          config_json_path: Optional[str] = None,  # 添加config_json_path参数
                          auto_close_app: bool = True,  # 是否在任务结束后自动关闭APP
                          search_keywords: Optional[List[str]] = None  # 添加search_keywords参数
                          ) -> Dict[str, Any]:
        """
        与多个笔记互动
        
        Args:
            target_type: 目标类型，可选值: keyword, user
            target: 目标内容，根据target_type决定是关键词还是用户名
            max_targets: 最大目标数量
            like_probability: 点赞概率
            collect_probability: 收藏概率
            comment_probability: 评论概率
            follow_probability: 关注概率
            comments: 评论内容列表
            duration_minutes: 任务时长(分钟)
            min_view_time: 最小浏览时间(秒)
            max_view_time: 最大浏览时间(秒)
            stay_in_search: 是否停留在搜索页
            comment_mode: 评论类型 "text" | "sticker" | "auto"
            sticker_id: 指定表情包id
            config_json_path: json配置文件路径，用于加载表情包模板
            auto_close_app: 是否在任务结束后自动关闭APP
            search_keywords: 搜索关键词列表
            
        Returns:
            互动结果
        """
        # 如果提供了config_json_path，重新加载表情包模板
        if config_json_path and os.path.exists(config_json_path):
            try:
                with open(config_json_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.sticker_templates = config_data.get('sticker_templates', [])
                logger.info(f"从配置文件加载表情包模板，数量: {len(self.sticker_templates)}")
            except Exception as e:
                logger.warning(f"加载sticker_templates失败: {e}")
        
        # 保存传入的search_keywords参数
        if search_keywords:
            self.search_keywords = search_keywords
            logger.info(f"使用传入的search_keywords: {search_keywords}")
        
        logger.info(f"[INTERACT] 当前sticker_templates数量: {len(self.sticker_templates)}")
        
        try:
            # 打开应用
            if not self.open_app():
                return {"status": "error", "message": "打开应用失败"}
            
            # 更新评论模板
            if comments:
                self.comment_templates = comments
                # 打印每个评论的预览，便于调试
                for i, comment in enumerate(comments[:3]):
                    logger.info(f"评论模板 {i+1} 预览: {comment[:30]}..." if len(comment) > 30 else f"评论模板 {i+1}: {comment}")
                logger.info(f"已更新评论模板，共 {len(comments)} 条")
                if len(comments) > 3:
                    logger.info(f"剩余 {len(comments)-3} 条评论模板省略显示")
            
            # 确定目标内容
            if target_type == "keyword":
                # 搜索关键词
                keyword = None
                # 先检查是否有直接传入的关键词
                if target and target.strip():
                    keyword = target
                    logger.info(f"使用直接传入的关键词: {keyword}")
                # 如果没有直接传入的关键词，但有search_keywords列表，使用第一个关键词
                elif hasattr(self, 'search_keywords') and self.search_keywords:
                    keyword = self.search_keywords[0]
                    logger.info(f"使用search_keywords列表中的第一个关键词: {keyword}")
                # 如果都没有，尝试获取随机关键词
                else:
                    keyword = self._get_random_keyword()
                
                if keyword is None:
                    return {
                        "status": "error", 
                        "message": "未提供搜索关键词且未找到有效的关键词列表"
                    }
                
                logger.info(f"搜索关键词: {keyword}")
                # 保存最后使用的关键词，以便在页面无法识别需要重启应用时能够恢复使用
                self.last_search_keyword = keyword
                
                if not self.search_keyword(keyword):
                    return {
                        "status": "error", 
                        "message": f"搜索关键词 {keyword} 失败"
                    }
                target_name = keyword
            else:  # user
                # 搜索用户
                username = target if target else (self.target_users[0] if self.target_users else None)
                if not username:
                    return {"status": "error", "message": "未提供目标用户"}
                logger.info(f"搜索用户: {username}")
                if not self.search_user(username):
                    return {"status": "error", "message": f"搜索用户 {username} 失败"}
                target_name = username
            
            # 记录开始时间
            start_time = time.time()

            # 重置当前关键词的追评时间累积
            self.total_follow_up_time = 0

            # 互动统计
            interactions = {
                "like": 0,
                "collect": 0,
                "comment": 0,
                "follow": 0
            }
            processed_count = 0
            
            # 验证当前是否在搜索结果页
            if not self.verify_page_type("search_results"):
                logger.warning("⚠️ 搜索后未能进入搜索结果页，尝试重新搜索")
                if target_type == "keyword":
                    if not self.search_keyword(keyword):
                        return {"status": "error", "message": f"重新搜索关键词 {keyword} 失败"}
                else:
                    if not self.search_user(username):
                        return {"status": "error", "message": f"重新搜索用户 {username} 失败"}
                
                # 再次验证是否在搜索结果页
                if not self.verify_page_type("search_results"):
                    return {"status": "error", "message": "无法进入搜索结果页，无法执行互动任务"}
            
            # 已确认在搜索结果页，进行筛选操作（无论是首次搜索成功还是重新搜索成功）
            logger.info("📝 进入搜索结果页后开始进行筛选")
            
            # 1. 点击筛选按钮
            logger.info("📝 点击筛选按钮")
            if not self.click_element("filter_button"):
                logger.warning("⚠️ 点击筛选按钮失败")
                # 尝试使用坐标点击
                try:
                    self.tap(600, 200)  # 使用预估坐标
                    logger.info("✅ 使用坐标点击筛选按钮")
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"❌ 点击筛选按钮失败: {str(e)}")
            else:
                logger.info("✅ 点击筛选按钮成功")
                time.sleep(1)
            
            # 2. 点击图文按钮
            logger.info("📝 点击图文按钮")
            if not self.click_element("image_text_button"):
                logger.warning("⚠️ 点击图文按钮失败")
                # 尝试使用坐标点击
                try:
                    self.tap(200, 250)  # 使用预估坐标
                    logger.info("✅ 使用坐标点击图文按钮")
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"❌ 点击图文按钮失败: {str(e)}")
            else:
                logger.info("✅ 点击图文按钮成功")
                time.sleep(1)
            
            # 3. 点击一周内按钮
            logger.info("📝 点击一周内按钮")
            if not self.click_element("one_week_button"):
                logger.warning("⚠️ 点击一周内按钮失败")
                # 尝试使用坐标点击
                try:
                    self.tap(200, 350)  # 使用预估坐标
                    logger.info("✅ 使用坐标点击一周内按钮")
                    time.sleep(1)
                except Exception as e:
                    logger.error(f"❌ 点击一周内按钮失败: {str(e)}")
            else:
                logger.info("✅ 点击一周内按钮成功")
                time.sleep(1)
            
            # 4. 点击筛选关闭按钮
            logger.info("📝 点击筛选关闭按钮")
            if not self.click_element("filter_close_button"):
                logger.warning("⚠️ 点击筛选关闭按钮失败")
                # 尝试使用坐标点击
                try:
                    self.tap(360, 500)  # 使用预估坐标
                    logger.info("✅ 使用坐标点击筛选关闭按钮")
                    time.sleep(2)
                except Exception as e:
                    logger.error(f"❌ 点击筛选关闭按钮失败: {str(e)}")
            else:
                logger.info("✅ 点击筛选关闭按钮成功")
                time.sleep(2)
            
            # 不再验证筛选后的页面状态，直接等待后继续
            logger.info("📝 筛选操作完成，等待1.5秒后继续")
            time.sleep(1.5)
            
            # 对搜索结果进行互动
            for i in range(max_targets):
                # 检查是否请求停止
                if self.check_stop_requested():
                    logger.info("检测到停止请求，中断互动任务")
                    break
                
                # 检查任务时长是否已经超过限制（排除追评时间）
                total_elapsed = time.time() - start_time  # 总耗时(秒)
                effective_duration = (total_elapsed - self.total_follow_up_time) / 60  # 有效时长(分钟)，排除追评时间

                if effective_duration >= duration_minutes:
                    logger.info(f"任务已达到设定时长 {duration_minutes:.2f} 分钟（有效时长，已排除 {self.total_follow_up_time:.1f} 秒追评时间），停止继续互动")
                    # 达到时长限制时关闭APP
                    if auto_close_app:
                        logger.info("达到任务时长限制，准备关闭小红书APP")
                        self.close_app()
                    break
                
                # 修改逻辑：先滑动查看搜索结果，然后再尝试点击
                # 滑动次数根据当前是第几次互动来决定
                swipe_count = 0  # 默认不滑动
                
                # 如果不是第一次循环，增加滑动次数
                if i > 0:
                    swipe_count = random.randint(1, 2)  # 随机滑动1-3次
                
                # 滑动前先确认当前页面状态
                if not self.verify_page_type("search_results"):
                    current_page = self.detect_current_page()
                    logger.warning(f"⚠️ 当前不在搜索结果页(检测到: {current_page})，尝试恢复")
                    
                    # 回到首页并重新搜索
                    if not self.go_to_home():
                        logger.error("❌ 无法回到首页，任务终止")
                        return {"status": "error", "message": "页面状态恢复失败"}
                    
                    # 重新搜索
                    if not self.search_keyword(self.last_search_keyword):
                        logger.error("❌ 重新搜索失败，任务终止")
                        return {"status": "error", "message": "重新搜索失败"}
                    
                    # 再次验证
                    if not self.verify_page_type("search_results"):
                        logger.error("❌ 重新搜索后仍未进入搜索结果页，任务终止")
                        return {"status": "error", "message": "页面状态恢复失败"}
                
                # 只有需要滑动时才显示日志和执行滑动
                if swipe_count > 0:
                    logger.info(f"📝 在搜索结果页先滑动 {swipe_count} 次查看内容")
                    
                    # 执行滑动操作
                    for j in range(swipe_count):
                        # 检查设备和任务状态
                        device_terminated = False
                        if self.device:
                            device_terminated = (hasattr(self.device, 'myt') and
                                               getattr(self.device, 'myt', None) and
                                               hasattr(getattr(self.device, 'myt', None), '_handle') and
                                               getattr(getattr(self.device, 'myt', None), '_handle', 1) == 0)

                        if self.is_terminated or self.check_stop_requested() or device_terminated:
                            logger.info("设备已终止或任务已请求停止，中断滑动操作")
                            break
                        
                        # 删除滑动前的页面验证代码 - 减少频繁验证
                        
                        logger.info(f"📝 搜索结果页滑动 ({j+1}/{swipe_count})")
                        
                        # 使用固定屏幕尺寸而不是动态获取
                        width = self.DEFAULT_SCREEN_WIDTH
                        height = self.DEFAULT_SCREEN_HEIGHT
                        
                        # 计算滑动参数 - 增加随机性
                        start_x = random.randint(int(width * 0.3), int(width * 0.7))  # 不再使用固定的中心点
                        start_y = int(height * 0.6) + random.randint(-10, 10)
                        end_x = start_x + random.randint(-30, 30)  # 允许轻微的水平偏移
                        end_y = int(height * 0.3) + random.randint(-10, 10)
                        duration = random.randint(500, 1500)
                        
                        try:
                            # 先尝试高级滑动方法
                            logger.debug(f"尝试使用贝塞尔曲线滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y}), duration={duration}ms")
                            
                            # 确保坐标在屏幕安全范围内
                            end_x = max(50, min(width - 50, end_x))
                            
                            # 添加贝塞尔曲线随机性
                            duration_sec = duration / 1000.0
                            natural_duration = duration_sec * random.uniform(0.9, 1.1)  # 滑动持续时间随机微调±10%
                            
                            # 水平方向添加轻微随机偏移，模拟手指自然晃动
                            bezier_start_x = start_x + random.randint(-15, 15)
                            bezier_end_x = end_x + random.randint(-15, 15)
                            
                            # 确保坐标在屏幕范围内
                            bezier_start_x = max(50, min(width - 50, bezier_start_x))
                            bezier_end_x = max(50, min(width - 50, bezier_end_x))
                            
                            logger.debug(f"贝塞尔曲线滑动(加随机性): ({bezier_start_x}, {start_y}) -> ({bezier_end_x}, {end_y}), 时长: {natural_duration:.2f}秒")
                            success = self.swipe_with_bezier(bezier_start_x, start_y, bezier_end_x, end_y, natural_duration)
                            
                            if not success:
                                logger.warning("⚠️ 贝塞尔曲线滑动失败，尝试普通滑动")
                                success = self.swipe(start_x, start_y, end_x, end_y, duration)
                        except Exception as e:
                            logger.warning(f"⚠️ 高级滑动异常: {str(e)}，尝试普通滑动")
                            try:
                                success = self.swipe(start_x, start_y, end_x, end_y, duration)
                            except Exception as e2:
                                logger.error(f"❌ 普通滑动也失败: {str(e2)}")
                                success = False
                        
                        # 每次滑动后短暂等待
                        wait_time = random.uniform(0.5, 1.5)
                        time.sleep(wait_time)
                    
                    # 所有滑动完成后，只验证一次页面状态
                    if not self.verify_page_type("search_results"):
                        current_page = self.detect_current_page()
                        logger.warning(f"⚠️ 滑动操作后页面类型为: {current_page}，尝试返回搜索结果页")
                        if not self.go_to_home(return_to_search=True):
                            logger.error("❌ 无法返回搜索结果页，中断任务")
                            # 不立即break，而是跳过本次尝试进入详情页
                            continue
                
                # 尝试进入详情页
                logger.info("📝 尝试点击进入详情页")
                if self.enter_detail_page():
                    processed_count += 1
                    
                    # 随机浏览时间
                    view_time = random.uniform(min_view_time, max_view_time)
                    logger.info(f"📝 计划在详情页浏览 {view_time:.1f} 秒")
                    
                    # 记录详情页浏览开始时间
                    detail_start_time = time.time()

                    # 重置追评操作时长（每个详情页开始时重置）
                    self.follow_up_duration = 0
                    
                    # 先在详情页上方区域进行水平右滑(向左滑动)
                    horizontal_swipe_times = random.randint(2, 4)
                    logger.info(f"📝 计划在详情页上方区域先进行 {horizontal_swipe_times} 次水平右滑")
                    
                    horizontal_actual_swipes = 0
                    for j in range(horizontal_swipe_times):
                        if self.check_stop_requested():
                            logger.info("⚠️ 检测到停止请求，中断详情页浏览")
                            break
                        
                        # 检查页面状态是否稳定
                        if i > 0:  # 第一次滑动前不检查
                            current_page = self.detect_current_page(hint="detail")
                            if current_page != "detail":
                                if current_page == "discovery":
                                    # 检测到是发现视频页，处理方式：等待一会后返回搜索结果页
                                    wait_time = random.uniform(1.0, 2.0)
                                    logger.info(f"⚠️ 检测到在发现视频页，等待 {wait_time:.1f} 秒后返回")
                                    time.sleep(wait_time)
                                    
                                    # 按返回键回到搜索结果页
                                    logger.info("按返回键回到搜索结果页")
                                    self.back()
                                    time.sleep(2)
                                    
                                    # 验证是否回到搜索结果页，不再接受普通搜索页
                                    if self.verify_page_type("search_results"):
                                        logger.info("✅ 成功从发现视频页返回搜索结果页")
                                    else:
                                        current_page = self.detect_current_page()
                                        logger.warning(f"⚠️ 返回后页面类型为: {current_page}，不是预期的搜索结果页")
                                    
                                    break
                                else:
                                    logger.warning(f"⚠️ 当前不在详情页(检测到: {current_page})，中断滑动浏览")
                                    break
                        
                        # 在屏幕上方区域进行水平右滑(从右往左滑)
                        width = self.DEFAULT_SCREEN_WIDTH
                        height = self.DEFAULT_SCREEN_HEIGHT
                        
                        # 在屏幕0.2至0.3高度位置进行水平滑动
                        swipe_y = int(height * random.uniform(0.2, 0.3))
                        start_x = int(width * 0.8)  # 从屏幕右侧开始
                        end_x = int(width * 0.2)    # 滑动到屏幕左侧
                        duration = random.randint(800, 1200)
                        
                        logger.debug(f"详情页上方区域水平右滑: ({start_x}, {swipe_y}) -> ({end_x}, {swipe_y}), duration={duration}ms")
                        
                        try:
                            # 先尝试高级滑动方法
                            # 添加更多随机性和自然性
                            duration_sec = duration / 1000.0
                            natural_duration = duration_sec * random.uniform(0.9, 1.1)  # 滑动持续时间随机微调±10%
                            
                            # 垂直方向添加轻微随机偏移
                            bezier_swipe_y = swipe_y + random.randint(-15, 15)
                            
                            # 水平方向也添加微小随机偏移
                            bezier_start_x = start_x + random.randint(-10, 10)
                            bezier_end_x = end_x + random.randint(-10, 10)
                            
                            # 确保坐标在屏幕范围内
                            bezier_swipe_y = max(50, min(height - 150, bezier_swipe_y))
                            bezier_start_x = max(50, min(width - 50, bezier_start_x))
                            bezier_end_x = max(50, min(width - 50, bezier_end_x))
                            
                            logger.debug(f"贝塞尔曲线水平滑动(加随机性): ({bezier_start_x}, {bezier_swipe_y}) -> ({bezier_end_x}, {bezier_swipe_y}), 时长: {natural_duration:.2f}秒")
                            success = self.swipe_with_bezier(bezier_start_x, bezier_swipe_y, bezier_end_x, bezier_swipe_y, natural_duration)
                            
                            if not success:
                                logger.warning("⚠️ 贝塞尔曲线滑动失败，尝试普通滑动")
                                success = self.swipe(start_x, swipe_y, end_x, swipe_y, duration)
                        except Exception as e:
                            logger.warning(f"⚠️ 高级滑动异常: {str(e)}，尝试普通滑动")
                            try:
                                success = self.swipe(start_x, swipe_y, end_x, swipe_y, duration)
                            except Exception as e2:
                                logger.error(f"❌ 普通滑动也失败: {str(e2)}")
                                success = False
                        
                        if success:
                            horizontal_actual_swipes += 1
                            logger.info(f"✅ 详情页成功进行水平右滑 ({j+1}/{horizontal_swipe_times})")
                        else:
                            logger.warning(f"⚠️ 详情页水平右滑失败 ({j+1}/{horizontal_swipe_times})")
                        
                        # 短暂等待
                        if self.random_wait(0.8, 2.0):
                            break
                    
                    logger.info(f"✅ 详情页共完成 {horizontal_actual_swipes}/{horizontal_swipe_times} 次水平右滑")
                    
                    # 在详情页内上下滑动浏览
                    swipe_times = random.randint(2, 5)
                    logger.info(f"📝 计划在详情页内滑动 {swipe_times} 次")
                    
                    # 随机上下滑动
                    actual_swipes = 0
                    for j in range(swipe_times):
                        if self.check_stop_requested():
                            logger.info("⚠️ 检测到停止请求，中断详情页浏览")
                            break
                            
                        # 检查页面状态是否稳定
                        if i > 0:  # 第一次滑动前不检查
                            current_page = self.detect_current_page(hint="detail")
                            if current_page != "detail":
                                if current_page == "discovery":
                                    # 检测到是发现视频页，处理方式：等待一会后返回搜索结果页
                                    wait_time = random.uniform(1.0, 2.0)
                                    logger.info(f"⚠️ 检测到在发现视频页，等待 {wait_time:.1f} 秒后返回")
                                    time.sleep(wait_time)
                                    
                                    # 按返回键回到搜索结果页
                                    logger.info("按返回键回到搜索结果页")
                                    self.back()
                                    time.sleep(2)
                                    
                                    # 验证是否回到搜索结果页，不再接受普通搜索页
                                    if self.verify_page_type("search_results"):
                                        logger.info("✅ 成功从发现视频页返回搜索结果页")
                                    else:
                                        current_page = self.detect_current_page()
                                        logger.warning(f"⚠️ 返回后页面类型为: {current_page}，不是预期的搜索结果页")
                                    
                                    break
                                else:
                                    logger.warning(f"⚠️ 当前不在详情页(检测到: {current_page})，中断滑动浏览")
                                    break
                        
                        # 随机选择滑动方向
                        direction = "下" if random.choice([True, False]) else "上"
                        logger.info(f"📝 计划在详情页内向{direction}滑动")
                        
                        width = self.DEFAULT_SCREEN_WIDTH
                        height = self.DEFAULT_SCREEN_HEIGHT
                        center_x = width // 2
                        
                        # 根据方向设置滑动参数
                        if direction == "下":  # 从下向上滑
                            start_y = int(height * 0.6)
                            end_y = int(height * 0.2)
                        else:  # 从上向下滑
                            start_y = int(height * 0.2)
                            end_y = int(height * 0.6)
                            
                        duration = random.randint(800, 1500)
                        
                        try:
                            # 先尝试高级滑动方法，增加随机性和自然性
                            # 1. 不再使用固定的屏幕中心点作为起点
                            start_x = random.randint(int(width * 0.3), int(width * 0.7))
                            
                            # 2. 终点添加轻微随机偏移
                            end_x = start_x + random.randint(-30, 30)
                            
                            # 3. 确保坐标在屏幕安全范围内
                            end_x = max(50, min(width - 50, end_x))
                            
                            # 4. 添加持续时间随机性
                            duration_sec = duration / 1000.0
                            natural_duration = duration_sec * random.uniform(0.9, 1.1)
                            
                            # 5. 贝塞尔曲线的控制点随机性
                            bezier_start_x = start_x + random.randint(-15, 15)
                            bezier_end_x = end_x + random.randint(-15, 15)
                            
                            # 6. 确保坐标在屏幕范围内
                            bezier_start_x = max(50, min(width - 50, bezier_start_x))
                            bezier_end_x = max(50, min(width - 50, bezier_end_x))
                            
                            logger.debug(f"贝塞尔曲线垂直滑动(加随机性): ({bezier_start_x}, {start_y}) -> ({bezier_end_x}, {end_y}), 时长: {natural_duration:.2f}秒")
                            success = self.swipe_with_bezier(bezier_start_x, start_y, bezier_end_x, end_y, natural_duration)
                            
                            if not success:
                                logger.warning("⚠️ 贝塞尔曲线滑动失败，尝试普通滑动")
                                success = self.swipe(start_x, start_y, end_x, end_y, duration)
                        except Exception as e:
                            logger.warning(f"⚠️ 高级滑动异常: {str(e)}，尝试普通滑动")
                            try:
                                success = self.swipe(center_x, start_y, center_x, end_y, duration)
                            except Exception as e2:
                                logger.error(f"❌ 普通滑动也失败: {str(e2)}")
                                success = False
                        
                        if success:
                            actual_swipes += 1
                            logger.info(f"✅ 详情页成功向{direction}滑动 ({j+1}/{swipe_times})")
                        else:
                            logger.warning(f"⚠️ 详情页向{direction}滑动失败 ({j+1}/{swipe_times})")
                        
                        # 短暂等待
                        if self.random_wait(2.0, 4.0):
                            break
                    
                    logger.info(f"✅ 详情页共完成 {actual_swipes}/{swipe_times} 次滑动")
                    
                    # 检查是否需要等待以满足最小浏览时间
                    elapsed_view_time = time.time() - detail_start_time

                    # 排除当前详情页的追评操作时间（如果有的话）
                    if hasattr(self, 'follow_up_duration') and self.follow_up_duration > 0:
                        elapsed_view_time -= self.follow_up_duration
                        logger.info(f"从详情页浏览时间中排除追评操作时间 {self.follow_up_duration:.1f} 秒（该时间已补偿到关键词总时长）")
                        self.follow_up_duration = 0  # 重置当前详情页的追评时间

                    remaining_time = view_time - elapsed_view_time
                    if remaining_time > 0:
                        logger.info(f"📝 详情页继续等待 {remaining_time:.1f} 秒以满足最小浏览时间")
                        time.sleep(remaining_time)
                    
                    # 决定是否执行评论
                    enable_comment = random.random() < comment_probability
                    
                    # 随机选择评论内容
                    comment_text = None
                    if comments and enable_comment and comment_mode == "text":
                        comment_text = random.choice(comments)
                    
                    # 执行互动
                    interact_result = self.interact_with_post(
                        like_probability=like_probability,
                        collect_probability=collect_probability,
                        follow_probability=follow_probability,
                        enable_comment=enable_comment,
                        comment_text=comment_text,
                        comment_mode=comment_mode,
                        sticker_id=sticker_id
                    )
                    
                    # 更新统计
                    for action, success in interact_result.items():
                        if success:
                            interactions[action] += 1
                    
                    # 检查是否已经在评论方法内执行了返回
                    already_returned = False
                    if enable_comment and interact_result.get("comment", False):
                        # 如果评论成功，则在interact_with_post方法中已经执行了返回操作
                        already_returned = True
                        # 不需要再执行返回，但需要验证页面状态
                        time.sleep(2)
                        if not self.verify_page_type("search_results"):
                            logger.warning("⚠️ 互动操作后未在搜索结果页，尝试恢复页面状态")
                            already_returned = False  # 需要重新返回
                    
                    # 只有在没有执行过返回时才执行返回
                    if not already_returned:
                        # 返回搜索结果页
                        self.back()  # 从详情页返回搜索结果页
                        time.sleep(2)
                    
                    # 验证是否成功返回搜索结果页
                    if not self.verify_page_type("search_results"):
                        logger.warning("⚠️ 从详情页返回后未能回到搜索结果页，尝试回到搜索结果页")
                        if not self.go_to_home(return_to_search=True):
                            logger.warning("⚠️ 无法回到搜索结果页，尝试重新搜索")
                            # 尝试回到首页并重新搜索
                            if self.go_to_home(return_to_search=False):
                                if target_type == "keyword":
                                    if not self.search_keyword(keyword):
                                        logger.error(f"⚠️ 重新搜索关键词 {keyword} 失败，中断任务")
                                        break
                                else:  # user
                                    if not self.search_user(username):
                                        logger.error(f"⚠️ 重新搜索用户 {username} 失败，中断任务")
                                        break
                                        
                                # 搜索后需要验证是否到达搜索结果页
                                time.sleep(3)
                                if not self.verify_page_type("search_results"):
                                    logger.warning("⚠️ 重新搜索后未能到达搜索结果页，可能需要额外处理")
                else:
                    # 如果进入详情页失败，记录日志并继续下一次循环
                    logger.warning("⚠️ 点击进入详情页失败，将在下一轮继续尝试")
                    
                    # 如果多次尝试点击都失败，可以考虑增加滑动次数
                    if i > 0 and processed_count == 0:
                        logger.warning("⚠️ 多次尝试点击都失败，增加滑动次数")
                        swipe_count = random.randint(2, 4)  # 增加滑动次数
                        for _ in range(swipe_count):
                            self.swipe_down()
                            time.sleep(random.uniform(0.5, 1.0))
            
            # 任务结束后，根据参数决定是否返回首页
            if not stay_in_search:
                logger.info("📝 任务结束，返回首页")
                self.go_to_home(return_to_search=False)
            else:
                logger.info("📝 任务结束，保持在搜索页")
            
            # 计算任务时长
            total_duration = (time.time() - start_time) / 60  # 总时长(分钟)
            effective_duration = (time.time() - start_time - self.total_follow_up_time) / 60  # 有效时长(分钟)，排除追评时间

            # 检查任务时长是否超过限制
            if total_duration > duration_minutes:
                logger.warning(f"警告：任务总时长超过限制，实际用时 {total_duration:.2f} 分钟，限制 {duration_minutes} 分钟")
                if self.total_follow_up_time > 0:
                    logger.info(f"其中追评操作耗时 {self.total_follow_up_time:.1f} 秒，有效互动时长 {effective_duration:.2f} 分钟")
                
            # 任务结束时关闭小红书APP
            if auto_close_app:
                logger.info("任务完成，准备关闭小红书APP")
                self.close_app()
            
            # 返回结果
            result = {
                "status": "success",
                "target_type": target_type,
                "target": target_name,
                "total_duration": round(total_duration, 2),
                "effective_duration": round(effective_duration, 2),
                "follow_up_time": round(self.total_follow_up_time, 1),
                "processed_count": processed_count,
                "interactions": interactions
            }

            # 打印任务总结
            if self.total_follow_up_time > 0:
                logger.info(f"任务总结: 目标 {target_name}，处理 {processed_count} 个笔记，" +
                           f"点赞 {interactions['like']}，收藏 {interactions['collect']}，" +
                           f"评论 {interactions['comment']}，关注 {interactions['follow']}，" +
                           f"总用时 {total_duration:.2f} 分钟（含追评 {self.total_follow_up_time:.1f} 秒），" +
                           f"有效互动时长 {effective_duration:.2f} 分钟")
            else:
                logger.info(f"任务总结: 目标 {target_name}，处理 {processed_count} 个笔记，" +
                           f"点赞 {interactions['like']}，收藏 {interactions['collect']}，" +
                           f"评论 {interactions['comment']}，关注 {interactions['follow']}，" +
                           f"用时 {total_duration:.2f} 分钟")
            
            return result
                
        except Exception as e:
            logger.error(f"执行互动任务异常: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e), "traceback": traceback.format_exc()}
    
    def check_stop_requested(self) -> bool:
        """
        检查是否请求停止任务
        
        Returns:
            是否请求停止
        """
        # 首先检查终止标记
        if self.is_terminated:
            logger.info("控制器已被标记为终止状态，停止任务")
            return True
            
        # 检查设备是否已终止或断开连接
        if self.device and hasattr(self.device, 'is_terminated') and getattr(self.device, 'is_terminated', False):
            logger.info("设备已被标记为终止状态，停止任务")
            self.is_terminated = True  # 同步终止状态
            return True

        # 检查设备连接是否已断开
        if not self.device or not hasattr(self.device, 'connected') or not getattr(self.device, 'connected', True):
            logger.info("设备连接已断开，停止任务")
            self.is_terminated = True  # 标记为终止状态
            return True
            
        # 最后检查基类的停止事件
        return super().check_stop_requested()

    def verify_page_type(self, page_type: str) -> bool:
        """
        快速验证当前页面类型，只查找带有page_type属性的模板
        
        Args:
            page_type: 要验证的页面类型
            
        Returns:
            是否为指定类型的页面
        """
        # 不显示详细日志，只在找到匹配时记录
        if not self.element_manager:
            return False

        indicators = self.element_manager.find_page_indicators(page_type, min_matches=1, fast_mode=True)
        if indicators:
            logger.info(f"[快速验证] 确认当前页面为'{page_type}'")
            self.current_page = page_type
            return True
        return False

    def execute(self,
              interaction_type: str = INTERACTION_TYPE_COMMENT,
              target_type: str = "keyword",
              target: Optional[str] = None,
              max_targets: int = 5,
              duration_minutes: float = 30.0,  # 添加任务时长参数
              min_view_time: float = 6.0,      # 最小浏览时间
              max_view_time: float = 13.0,     # 最大浏览时间
              comment_text: Optional[str] = None,
              comments: Optional[List[str]] = None,
              like_probability: float = 0.7,
              collect_probability: float = 0.3,
              comment_probability: float = 1.0,
              follow_probability: float = 0.1,
              do_like: bool = False,
              do_collect: bool = False,
              do_follow: bool = False,
              comments_file_path: Optional[str] = None,
              keywords_file_path: Optional[str] = None,
              stay_in_search: bool = True,     # 是否保持在搜索页
              comment_mode: str = "text",      # 评论类型
              sticker_templates: Optional[list] = None,  # 表情包模板
              sticker_id: Optional[int] = None,  # 指定表情包id
              config_json_path: Optional[str] = None,  # 添加config_json_path参数
              auto_close_app: bool = True,    # 是否在任务结束后自动关闭APP
              enable_follow_up_comment: bool = False,  # 是否启用追评功能
              follow_up_comments: Optional[List[str]] = None,  # 追评文本列表
              **kwargs) -> Dict[str, Any]:
        """
        执行互动任务
        
        Args:
            interaction_type: 互动类型，可选值: comment, like, collect, follow
            target_type: 目标类型，可选值: keyword, user
            target: 目标内容，根据target_type决定是关键词还是用户名
            max_targets: 最大目标数量
            duration_minutes: 任务时长(分钟)
            min_view_time: 最小浏览时间(秒)
            max_view_time: 最大浏览时间(秒)
            comment_text: 评论内容，如果不提供则使用随机评论
            comments: 评论内容列表，如果提供则覆盖默认评论模板
            like_probability: 点赞概率
            collect_probability: 收藏概率
            comment_probability: 评论概率
            follow_probability: 关注概率
            do_like: 是否执行点赞（兼容旧版参数）
            do_collect: 是否执行收藏（兼容旧版参数）
            do_follow: 是否执行关注（兼容旧版参数）
            comments_file_path: 评论文件路径，如果提供则从文件读取评论
            keywords_file_path: 关键词文件路径，如果提供则从文件读取关键词
            stay_in_search: 是否保持在搜索页
            comment_mode: 评论类型 "text" | "sticker" | "auto"
            sticker_templates: 表情包模板列表
            sticker_id: 指定表情包id
            config_json_path: json配置文件路径，用于加载表情包模板
            auto_close_app: 是否在任务结束后自动关闭APP
            **kwargs: 其他参数
            
        Returns:
            任务执行结果
        """
        # 重要：重置控制器的终止状态，确保新任务能正常开始
        self.is_terminated = False
        
        # 如果设备被标记为已终止，尝试重置状态
        if self.device and hasattr(self.device, 'is_terminated'):
            setattr(self.device, 'is_terminated', False)
            logger.info("重置设备终止状态，确保任务可以正常开始")
        
        # 如果设备连接断开，尝试重新连接
        if (self.device and
            hasattr(self.device, 'connected') and
            not self.device.connected and
            hasattr(self.device, 'connect')):
            logger.info("设备连接已断开，尝试重新连接")
            self.device.connect()
            # 等待短暂时间以确保连接稳定
            time.sleep(1)
            
        # 首先检查是否请求停止
        if self.is_terminated:
            logger.info("控制器已经被标记为终止状态，不执行互动任务")
            return {"status": "terminated", "message": "任务已被终止"}
            
        if self.check_stop_requested():
            logger.info("检测到停止请求，不执行互动任务")
            return {"status": "stopped", "message": "检测到停止请求"}
            
        try:
            # 记录任务开始和参数
            logger.info("========== 互动任务开始执行 ==========")
            logger.info(f"📝 互动类型: {interaction_type}")
            logger.info(f"📝 目标类型: {target_type}")
            logger.info(f"📝 目标内容: {target if target else '随机'}")
            logger.info(f"📝 最大目标数: {max_targets}")
            logger.info(f"📝 计划时长: {duration_minutes}分钟")
            logger.info(f"📝 浏览时间: {min_view_time}秒 ~ {max_view_time}秒")
            logger.info(f"📝 互动概率: 点赞 {like_probability*100:.0f}%, 收藏 {collect_probability*100:.0f}%, " +
                       f"评论 {comment_probability*100:.0f}%, 关注 {follow_probability*100:.0f}%")
            logger.info(f"📝 评论模式: {comment_mode}")

            # 设置追评相关属性
            self.enable_follow_up = enable_follow_up_comment
            if follow_up_comments:
                self.follow_up_templates = follow_up_comments
                logger.info(f"📝 追评功能状态: 启用，追评文本数量: {len(follow_up_comments)}")
            else:
                self.follow_up_templates = []
                logger.info(f"📝 追评功能状态: {'启用但无追评文本' if enable_follow_up_comment else '禁用'}")

            # 如果提供了config_json_path，重新加载表情包模板
            if config_json_path and os.path.exists(config_json_path):
                try:
                    with open(config_json_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    self.sticker_templates = config_data.get('sticker_templates', [])
                    logger.info(f"从配置文件加载表情包模板，数量: {len(self.sticker_templates)}")
                except Exception as e:
                    logger.warning(f"加载sticker_templates失败: {e}")
            
            # 如果提供了sticker_templates，使用传入的模板
            if sticker_templates:
                self.sticker_templates = sticker_templates
                logger.info(f"使用传入的表情包模板，数量: {len(self.sticker_templates)}")
            
            logger.info(f"[EXECUTE] 当前sticker_templates数量: {len(self.sticker_templates)}")
            
            # 处理评论文件路径（如果提供）
            if comments_file_path and os.path.exists(comments_file_path):
                try:
                    file_ext = os.path.splitext(comments_file_path)[1].lower()
                    file_comments = []
                    
                    # 只处理Excel文件
                    if file_ext in ['.xlsx', '.xls']:
                        try:
                            import pandas as pd
                            # 读取Excel文件的A列数据
                            logger.info(f"尝试从Excel文件读取评论: {comments_file_path}")
                            df = pd.read_excel(comments_file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
                            # 获取第一列的所有非空值，每个单元格作为一个完整评论
                            if not df.empty and len(df.columns) > 0:
                                col_values = df.iloc[:, 0].dropna().tolist()
                                file_comments = [str(val).strip() for val in col_values if str(val).strip()]
                                logger.info(f"成功从Excel文件 {comments_file_path} 的A列读取 {len(file_comments)} 条评论模板")
                                # 显示前三条评论内容预览
                                if file_comments:
                                    for i, comment in enumerate(file_comments[:3]):
                                        logger.info(f"评论模板 {i+1} 预览: {comment[:30]}...")
                            else:
                                logger.warning(f"Excel文件 {comments_file_path} 为空或A列不存在")
                        except ImportError:
                            logger.error("无法导入pandas库，请确保已安装pandas和openpyxl: pip install pandas openpyxl")
                        except Exception as e:
                            logger.error(f"读取Excel评论文件失败: {str(e)}")
                    else:
                        logger.warning(f"不支持的文件格式: {file_ext}，请使用Excel文件(.xlsx或.xls)")
                    
                    if file_comments:
                        logger.info(f"成功从文件读取 {len(file_comments)} 条评论模板")
                        comments = file_comments
                        
                        # 重要：将评论模板同时保存到self.comment_templates中，供_get_random_comment使用
                        self.comment_templates = file_comments
                        logger.info(f"成功加载 {len(self.comment_templates)} 条评论模板到控制器")
                        
                        # 检查评论模板中是否包含换行符
                        has_newlines = False
                        for i, comment in enumerate(comments[:3]):
                            if "\n" in comment:
                                has_newlines = True
                                logger.info(f"评论模板 {i+1} 包含换行符，但将作为一个整体使用")
                        
                        if has_newlines:
                            logger.info("检测到评论模板中包含换行符，每个单元格内容将作为一个完整评论模板使用")
                    else:
                        logger.warning(f"评论文件内容为空或格式不支持: {comments_file_path}")
                except Exception as e:
                    logger.error(f"读取评论文件失败: {str(e)}")
            
            # 检查评论功能是否可用
            if comment_mode == "text" and not self.comment_templates and (not comments or not len(comments)):
                logger.warning("⚠️ 启用了文本评论但未提供评论内容，评论功能可能无法正常工作")
            elif comment_mode == "sticker" and not self.sticker_templates:
                if sticker_templates:
                    logger.info(f"使用传入的表情包模板，共 {len(sticker_templates)} 个")
                    self.sticker_templates = sticker_templates
                else:
                    logger.warning("⚠️ 启用了表情包评论但未配置表情包模板，评论功能可能无法正常工作")
            elif comment_mode == "auto":
                # 随机模式且没有评论时自动切换为表情包评论
                has_comment_templates = self.comment_templates and len(self.comment_templates) > 0
                has_comments_param = comments and len(comments) > 0
                
                # 如果没有评论内容，强调会使用表情包
                if not has_comment_templates and not has_comments_param:
                    logger.info("⚠️ 随机模式但未提供评论模板，将默认使用表情包评论")
                    # 检查是否有表情包模板
                    if not self.sticker_templates and not sticker_templates:
                        logger.warning("⚠️ 未配置表情包模板，评论功能可能无法正常工作")
                    else:
                        # 有表情包，确保使用表情包
                        sticker_count = 0
                        if self.sticker_templates:
                            sticker_count = len(self.sticker_templates)
                        elif sticker_templates:
                            sticker_count = len(sticker_templates)
                        logger.info(f"使用系统内置表情包，共 {sticker_count} 个")
                else:
                    # 安全地获取评论模板长度
                    comment_count = 0
                    if self.comment_templates:
                        comment_count = len(self.comment_templates)
                    elif comments:
                        comment_count = len(comments)
                    logger.info(f"随机模式：有{comment_count}条评论模板可用")
            
            # 处理关键词文件路径（如果提供）与提取搜索关键词列表
            search_keywords = []
            
            # 从kwargs中获取search_keywords参数
            if 'search_keywords' in kwargs and kwargs['search_keywords']:
                search_keywords = kwargs['search_keywords']
                logger.info(f"使用kwargs中传入的search_keywords: {search_keywords}")
            
            # 如果直接提供了target(非空)，将其作为第一个关键词
            if target and target.strip():
                if ',' in target:
                    # 如果target包含逗号，将其拆分为多个关键词
                    keywords = [k.strip() for k in target.split(',') if k.strip()]
                    search_keywords.extend(keywords)
                    logger.info(f"从目标内容中提取了 {len(keywords)} 个关键词: {', '.join(keywords)}")
                else:
                    # 否则作为单个关键词添加
                    search_keywords.append(target.strip())
                    logger.info(f"使用目标内容作为关键词: {target}")
            
            # 再处理关键词文件
            if keywords_file_path and os.path.exists(keywords_file_path) and target_type == "keyword":
                try:
                    logger.info(f"尝试从文件读取关键词: {keywords_file_path}")
                    with open(keywords_file_path, 'r', encoding='utf-8') as f:
                        file_keywords = [line.strip() for line in f if line.strip()]
                    
                    if file_keywords:
                        logger.info(f"成功从文件读取 {len(file_keywords)} 个关键词")
                        # 只有在search_keywords为空时才完全使用文件关键词
                        # 否则将文件关键词追加到现有关键词后
                        if not search_keywords:
                            search_keywords = file_keywords
                        else:
                            search_keywords.extend(file_keywords)
                            logger.info(f"将文件关键词追加到现有关键词，现共有 {len(search_keywords)} 个关键词")
                    else:
                        logger.warning(f"关键词文件内容为空: {keywords_file_path}")
                except UnicodeDecodeError:
                    # 尝试其他编码
                    try:
                        with open(keywords_file_path, 'r', encoding='gbk') as f:
                            file_keywords = [line.strip() for line in f if line.strip()]
                        if file_keywords:
                            logger.info(f"成功使用GBK编码从文件读取 {len(file_keywords)} 个关键词")
                            if not search_keywords:
                                search_keywords = file_keywords
                            else:
                                search_keywords.extend(file_keywords)
                                logger.info(f"将文件关键词追加到现有关键词，现共有 {len(search_keywords)} 个关键词")
                    except Exception as e:
                        logger.error(f"读取关键词文件失败: {str(e)}")
                except Exception as e:
                    logger.error(f"读取关键词文件失败: {str(e)}")
            
            # 更新搜索关键词
            if search_keywords:
                self.search_keywords = search_keywords
                logger.info(f"已更新搜索关键词，共 {len(search_keywords)} 个")
            
            # 兼容旧版参数（布尔型互动设置）
            if do_like:
                like_probability = 1.0
            if do_collect:
                collect_probability = 1.0
            if do_follow:
                follow_probability = 1.0
            
            # 处理单一评论文本和评论列表的关系
            if comment_text and not comments:
                comments = [comment_text]
                logger.info(f"使用单一评论内容: {comment_text[:30]}...")
            
            # 如果是关键词搜索，且有多个关键词，平均分配时间
            if target_type == "keyword" and search_keywords and len(search_keywords) > 1:
                logger.info(f"检测到多个关键词({len(search_keywords)}个)，将随机选择3-5个关键词执行任务")
                
                # 随机选择3-5个关键词
                num_keywords_to_use = min(random.randint(3, 5), len(search_keywords))
                selected_keywords = random.sample(search_keywords, num_keywords_to_use)
                
                logger.info(f"从{len(search_keywords)}个关键词中随机选择了{num_keywords_to_use}个: {', '.join(selected_keywords)}")
                
                # 计算每个关键词的时长（分钟）
                keyword_duration = duration_minutes / num_keywords_to_use
                logger.info(f"每个关键词分配时长: {keyword_duration:.2f}分钟")
                
                # 任务整体结果
                overall_result = {
                    "status": "success",
                    "target_type": target_type,
                    "total_keywords": len(search_keywords),
                    "selected_keywords": num_keywords_to_use,
                    "keywords_used": selected_keywords,
                    "total_duration": 0,
                    "processed_count": 0,
                    "keywords_results": [],
                    "interactions": {
                        "like": 0,
                        "collect": 0,
                        "comment": 0,
                        "follow": 0
                    }
                }
                
                # 记录任务开始时间
                overall_start_time = time.time()
                
                # 执行每个选定关键词的互动任务
                for i, keyword in enumerate(selected_keywords):
                    # 检查是否已经超过总时长限制
                    current_overall_duration = (time.time() - overall_start_time) / 60
                    if current_overall_duration >= duration_minutes:
                        logger.warning(f"已达到总任务时长限制({duration_minutes}分钟)，停止后续关键词处理")
                        # 达到时长限制时关闭APP
                        if auto_close_app:
                            logger.info("达到总任务时长限制，准备关闭小红书APP")
                            self.close_app()
                        break
                    
                    # 计算剩余可用时间
                    remaining_time = max(0, duration_minutes - current_overall_duration)
                    # 计算当前关键词应分配的时间（考虑剩余关键词平均分配）
                    remaining_keywords = len(selected_keywords) - i
                    current_keyword_duration = min(keyword_duration, remaining_time / remaining_keywords)
                    
                    logger.info(f"开始处理第{i+1}/{num_keywords_to_use}个关键词: {keyword}, 分配时长: {current_keyword_duration:.2f}分钟")
                    
                    # 是否在最后一个关键词结束后返回首页
                    is_last_keyword = (i == len(selected_keywords) - 1)
                    current_stay_in_search = stay_in_search and not is_last_keyword
                    
                    # 执行单个关键词的互动任务
                    result = self.interact_with_posts(
                        target_type="keyword",
                        target=keyword,
                        max_targets=max_targets,
                        like_probability=like_probability,
                        collect_probability=collect_probability,
                        comment_probability=comment_probability,
                        follow_probability=follow_probability,
                        comments=comments,
                        duration_minutes=current_keyword_duration,
                        min_view_time=min_view_time,
                        max_view_time=max_view_time,
                        stay_in_search=current_stay_in_search,
                        comment_mode=comment_mode,
                        sticker_id=sticker_id,
                        config_json_path=config_json_path,
                        auto_close_app=auto_close_app,
                        search_keywords=[keyword]  # 确保始终传递当前关键词作为列表
                    )
                    
                    # 更新整体结果
                    if result["status"] == "success":
                        overall_result["total_duration"] += result["total_duration"]
                        overall_result["processed_count"] += result["processed_count"]
                        
                        # 更新互动统计
                        for action in ["like", "collect", "comment", "follow"]:
                            overall_result["interactions"][action] += result["interactions"][action]
                        
                        # 记录单个关键词结果
                        overall_result["keywords_results"].append({
                            "keyword": keyword,
                            "duration": result["total_duration"],
                            "processed_count": result["processed_count"],
                            "interactions": result["interactions"]
                        })
                    else:
                        # 如果某个关键词处理失败，记录错误但继续处理
                        logger.error(f"关键词 {keyword} 处理失败: {result.get('message', '未知错误')}")
                        overall_result["keywords_results"].append({
                            "keyword": keyword,
                            "status": "error",
                            "message": result.get("message", "未知错误")
                        })
                
                # 任务结束，返回整体结果
                overall_result["total_duration"] = round(overall_result["total_duration"], 2)
                
                # 任务总结
                logger.info(f"多关键词任务总结: 从{len(search_keywords)}个关键词中选择了{num_keywords_to_use}个，" +
                          f"完成处理 {len(overall_result['keywords_results'])}/{num_keywords_to_use}个关键词，" +
                          f"处理笔记 {overall_result['processed_count']} 个，" +
                          f"点赞 {overall_result['interactions']['like']}，" +
                          f"收藏 {overall_result['interactions']['collect']}，" +
                          f"评论 {overall_result['interactions']['comment']}，" +
                          f"关注 {overall_result['interactions']['follow']}，" +
                          f"用时 {overall_result['total_duration']} 分钟")
                
                # 任务结束时关闭小红书APP
                if auto_close_app:
                    logger.info("多关键词任务完成，准备关闭小红书APP")
                    self.close_app()
                
                return overall_result
            else:
                # 单个目标的处理逻辑（关键词或用户）
                target_value = None
                if target_type == "keyword":
                    # 修改这里：先使用search_keywords中的第一个关键词，即使target为空
                    if search_keywords:
                        target_value = search_keywords[0]  # 优先使用search_keywords中的第一个关键词
                    elif target:
                        target_value = target  # 如果没有search_keywords但有target，使用target
                    # 如果都没有，target_value保持None
                else:  # user
                    target_value = target
                
                # 使用互动方法执行任务
                result = self.interact_with_posts(
                    target_type=target_type,
                    target=target_value,
                    max_targets=max_targets,
                    like_probability=like_probability,
                    collect_probability=collect_probability,
                    comment_probability=comment_probability,
                    follow_probability=follow_probability,
                    comments=comments,
                    duration_minutes=duration_minutes,
                    min_view_time=min_view_time,
                    max_view_time=max_view_time,
                    stay_in_search=stay_in_search,
                    comment_mode=comment_mode,
                    sticker_id=sticker_id,
                    config_json_path=config_json_path,
                    auto_close_app=auto_close_app,
                    search_keywords=search_keywords
                )
                
        except Exception as e:
            logger.error(f"执行互动任务异常: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e), "traceback": traceback.format_exc()} 
        
        # 最后，记录表情包使用情况
        if sticker_templates is not None:
            self.sticker_templates = sticker_templates
        logger.info(f"[EXECUTE] 当前sticker_templates数量: {len(self.sticker_templates)}")
        
        return result

    def swipe_with_bezier(self, 
                        start_x: int, 
                        start_y: int, 
                        end_x: int, 
                        end_y: int, 
                        duration: float = 1.0,
                        **kwargs) -> bool:
        """
        使用贝塞尔曲线实现更自然的滑动轨迹
        
        Args:
            start_x: 起始点x坐标
            start_y: 起始点y坐标
            end_x: 结束点x坐标
            end_y: 结束点y坐标
            duration: 滑动持续时间(秒)
            **kwargs: 其他可选参数，如control_points和stop_event
            
        Returns:
            是否滑动成功
        """
        try:
            # 检查父类是否有此方法，有则调用父类方法
            if hasattr(super(), 'swipe_with_bezier'):
                return super().swipe_with_bezier(
                    start_x, start_y, end_x, end_y, duration, **kwargs
                )
            
            # 检查设备是否直接支持贝塞尔曲线滑动
            if hasattr(self.device, 'swipe_with_bezier'):
                # 添加随机性以模拟人类操作
                # 1. 为起点和终点坐标添加轻微随机偏移
                natural_start_x = start_x + random.randint(-15, 15)
                natural_end_x = end_x + random.randint(-15, 15)
                
                # 2. 为垂直滑动添加轻微水平抖动
                if abs(start_y - end_y) > abs(start_x - end_x):  # 主要是垂直滑动
                    natural_start_y = start_y
                    natural_end_y = end_y
                else:  # 主要是水平滑动
                    # 添加垂直方向的轻微偏移
                    natural_start_y = start_y + random.randint(-15, 15)
                    natural_end_y = end_y + random.randint(-15, 15)
                
                # 3. 获取屏幕尺寸以确保坐标在安全范围内
                width = self.DEFAULT_SCREEN_WIDTH
                height = self.DEFAULT_SCREEN_HEIGHT
                
                # 确保坐标在屏幕范围内
                natural_start_x = max(50, min(width - 50, natural_start_x))
                natural_end_x = max(50, min(width - 50, natural_end_x))
                natural_start_y = max(50, min(height - 50, natural_start_y))
                natural_end_y = max(50, min(height - 50, natural_end_y))
                
                # 4. 为滑动持续时间添加轻微随机变化 (±10%)
                natural_duration = duration * random.uniform(0.9, 1.1)
                
                logger.debug(f"贝塞尔曲线滑动(加随机性): ({natural_start_x}, {natural_start_y}) -> ({natural_end_x}, {natural_end_y}), 时长: {natural_duration:.2f}秒")
                
                if self.device and hasattr(self.device, 'swipe_with_bezier'):
                    result = self.device.swipe_with_bezier(
                        natural_start_x, natural_start_y, natural_end_x, natural_end_y, natural_duration, **kwargs
                    )
                else:
                    logger.warning("设备不支持swipe_with_bezier方法，使用普通滑动")
                    result = self.swipe(natural_start_x, natural_start_y, natural_end_x, natural_end_y, int(natural_duration * 1000))
                self.record_action(f"贝塞尔曲线滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y})", result)
                return result
            
            # 如果都不支持，退化为普通滑动
            logger.warning("当前环境不支持贝塞尔曲线滑动，将使用普通滑动替代")
            return self.swipe(start_x, start_y, end_x, end_y, int(duration * 1000))
        except Exception as e:
            logger.error(f"贝塞尔曲线滑动异常: {str(e)}", exc_info=True)
            self.record_action(f"贝塞尔曲线滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y})", False)
            return False

    def close_app(self) -> bool:
        """
        关闭小红书应用
        
        Returns:
            bool: 是否成功关闭
        """
        try:
            logger.info("尝试关闭小红书应用")
            
            # 首先检测当前页面
            current_page = self.detect_current_page()
            
            # 如果在详情页，先返回搜索结果页
            if current_page == "detail":
                self.back()
                time.sleep(1)
                
            # 根据当前页面决定需要按几次back键
            current_page = self.detect_current_page()
            if current_page == "search_results":
                # 从搜索结果页需要按两次返回
                back_count = 2
            elif current_page == "search":
                # 从搜索页需要按一次返回
                back_count = 1
            elif current_page == "home":
                # 从首页直接使用应用切换器关闭
                back_count = 2
            else:
                # 其他页面按三次back键
                back_count = 3
                
            # 执行多次back操作
            logger.info(f"执行{back_count}次back键操作关闭应用")
            for _ in range(back_count):
                self.back()
                time.sleep(0.8)
                
            logger.info("✅ 已尝试通过Back键退出应用")
            return True
                
        except Exception as e:
            logger.error(f"关闭应用异常: {str(e)}")
            return False

    def restart_app(self) -> bool:
        """
        重启小红书应用
        
        Returns:
            是否成功
        """
        logger.info("尝试重启小红书应用")
        
        # 保存当前状态以便重启后恢复
        saved_search_keywords = getattr(self, 'search_keywords', None)
        if not hasattr(self, 'last_search_keyword'):
            self.last_search_keyword = None
        saved_target = self.last_search_keyword
        
        # 调用父类的restart_app方法实际执行重启
        result = super().restart_app()
        
        # 恢复保存的状态
        if saved_search_keywords:
            self.search_keywords = saved_search_keywords
            logger.info(f"应用重启后恢复搜索关键词: {saved_search_keywords}")
        
        if saved_target:
            self.last_search_keyword = saved_target
            logger.info(f"应用重启后恢复最后搜索关键词: {saved_target}")
        
        return result