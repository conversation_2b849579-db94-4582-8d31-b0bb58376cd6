2025-07-29 02:15:14,269 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔧 创建增强RPC客户端: 10.0.0.127:11050
2025-07-29 02:15:14,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🏷️ 容器名称已设置: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 02:15:14,274 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔒 安全上下文已设置
2025-07-29 02:15:14,274 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 所有安全参数完整
2025-07-29 02:15:14,275 - xhs_matrix.core.enhanced_rpc_client - INFO - 🚀 开始连接设备流程: 10.0.0.127:11050
2025-07-29 02:15:14,275 - xhs_matrix.core.enhanced_rpc_client - INFO - 📋 连接配置: 最大重试3次, 连接超时3秒, 重试间隔5秒
2025-07-29 02:15:14,276 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔗 [第1/3次] 开始连接尝试: 10.0.0.127:11050
2025-07-29 02:15:14,276 - xhs_matrix.core.enhanced_rpc_client - INFO - SDK动态库路径: d:\xhs_matrix_v2.0.0\demo_py_x64\lib\libmytrpc.dll
2025-07-29 02:15:14,278 - xhs_matrix.core.enhanced_rpc_client - INFO - 检测到SDK版本: 10
2025-07-29 02:15:14,311 - xhs_matrix.core.enhanced_rpc_client - INFO - 工作模式设置成功: ACCESSIBILITY_OFF
2025-07-29 02:15:14,311 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ [第1次] 设备连接成功: 10.0.0.127:11050
2025-07-29 02:15:14,311 - xhs_matrix.core.enhanced_rpc_client - INFO - ⏱️ 连接耗时: 本次尝试0.04秒, 总耗时0.04秒
2025-07-29 02:15:14,316 - xhs_matrix.core.health_monitor_manager - INFO - 全局健康监控管理器初始化完成
2025-07-29 02:15:14,316 - xhs_matrix.core.health_monitoring - INFO - 创建连接健康监控器: e4846b05-ed15-4014-9a11-861d0b79a2be -> 10.0.0.127:11050[5]
2025-07-29 02:15:14,316 - xhs_matrix.core.health_monitoring - INFO - 健康监控线程启动: e4846b05-ed15-4014-9a11-861d0b79a2be
2025-07-29 02:15:14,316 - xhs_matrix.core.health_monitoring - INFO - 健康监控启动成功: e4846b05-ed15-4014-9a11-861d0b79a2be
2025-07-29 02:15:14,316 - xhs_matrix.core.health_monitoring - INFO - 健康监控等待初始延迟 10 秒，确保连接稳定...
2025-07-29 02:15:14,317 - xhs_matrix.core.health_monitor_manager - INFO - ✅ 注册健康监控器: 10.0.0.127:5 (任务: e4846b05-ed15-4014-9a11-861d0b79a2be)
2025-07-29 02:15:14,317 - xhs_matrix.core.health_monitor_manager - INFO - 📊 当前活跃监控器总数: 1
2025-07-29 02:15:14,318 - xhs_matrix.core.health_monitor_bridge - INFO - 健康监控桥接器初始化完成: D:\xhs_matrix_v2.0.0\xhs_matrix\logs\health_bridge
2025-07-29 02:15:25,604 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (498, 856) -> (516, 310), 时长: 0.749254823368315秒
2025-07-29 02:15:26,355 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (498, 856) 到 (516, 310), 持续 0.749254823368315秒, 成功率: 100.0%
2025-07-29 02:15:28,064 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (573, 802) -> (553, 303), 时长: 0.9288875673671779秒
2025-07-29 02:15:28,994 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (573, 802) 到 (553, 303), 持续 0.9288875673671779秒, 成功率: 100.0%
2025-07-29 02:15:31,303 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (369, 958) -> (350, 485), 时长: 1.**********276003秒
2025-07-29 02:15:32,703 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (369, 958) 到 (350, 485), 持续 1.**********276003秒, 成功率: 100.0%
2025-07-29 02:15:39,993 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 340) -> (144, 337), 时长: 0.8018542900930976秒
2025-07-29 02:15:40,796 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 340) 到 (144, 337), 持续 0.8018542900930976秒, 成功率: 100.0%
2025-07-29 02:15:43,005 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (540, 896) -> (540, 384), 时长: 0.832秒
2025-07-29 02:15:43,838 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (540, 896) 到 (540, 384), 持续 0.832秒, 成功率: 100.0%
2025-07-29 02:15:45,142 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (312, 896) -> (312, 384), 时长: 0.568秒
2025-07-29 02:15:45,712 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (312, 896) 到 (312, 384), 持续 0.568秒, 成功率: 100.0%
2025-07-29 02:15:50,267 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (520, 896) -> (520, 384), 时长: 0.723秒
2025-07-29 02:15:50,991 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (520, 896) 到 (520, 384), 持续 0.723秒, 成功率: 100.0%
2025-07-29 02:15:52,899 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (460, 384) -> (460, 896), 时长: 0.862秒
2025-07-29 02:15:53,762 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (460, 384) 到 (460, 896), 持续 0.862秒, 成功率: 100.0%
2025-07-29 02:16:03,232 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (360, 256) -> (335, 640), 时长: 0.8秒
2025-07-29 02:16:04,033 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (360, 256) 到 (335, 640), 持续 0.8秒, 成功率: 100.0%
2025-07-29 02:16:19,595 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (490, 1120) -> (516, 562), 时长: 0.6756020129903889秒
2025-07-29 02:16:20,272 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (490, 1120) 到 (516, 562), 持续 0.6756020129903889秒, 成功率: 100.0%
2025-07-29 02:16:26,959 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 351) -> (144, 316), 时长: 0.840341428109107秒
2025-07-29 02:16:27,800 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 351) 到 (144, 316), 持续 0.840341428109107秒, 成功率: 100.0%
2025-07-29 02:16:29,910 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (487, 896) -> (487, 384), 时长: 0.903秒
2025-07-29 02:16:30,814 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (487, 896) 到 (487, 384), 持续 0.903秒, 成功率: 100.0%
2025-07-29 02:16:32,719 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (447, 896) -> (447, 384), 时长: 0.767秒
2025-07-29 02:16:33,487 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (447, 896) 到 (447, 384), 持续 0.767秒, 成功率: 100.0%
2025-07-29 02:16:37,590 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (301, 896) -> (301, 384), 时长: 0.903秒
2025-07-29 02:16:38,494 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (301, 896) 到 (301, 384), 持续 0.903秒, 成功率: 100.0%
2025-07-29 02:16:42,774 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (507, 384) -> (507, 896), 时长: 1.2921603024062314秒
2025-07-29 02:16:44,067 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (507, 384) 到 (507, 896), 持续 1.2921603024062314秒, 成功率: 100.0%
2025-07-29 02:16:44,904 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (299, 384) -> (286, 896), 时长: 1.3778777632060262秒
2025-07-29 02:16:46,283 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (299, 384) 到 (286, 896), 持续 1.3778777632060262秒, 成功率: 100.0%
2025-07-29 02:16:47,717 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (393, 384) -> (381, 896), 时长: 0.9628214818413984秒
2025-07-29 02:16:48,681 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (393, 384) 到 (381, 896), 持续 0.9628214818413984秒, 成功率: 100.0%
2025-07-29 02:16:49,896 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (419, 384) -> (406, 896), 时长: 1.5549809193997006秒
2025-07-29 02:16:51,452 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (419, 384) 到 (406, 896), 持续 1.5549809193997006秒, 成功率: 100.0%
2025-07-29 02:16:52,665 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (314, 384) -> (330, 896), 时长: 0.9820691904562945秒
2025-07-29 02:16:53,648 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (314, 384) 到 (330, 896), 持续 0.9820691904562945秒, 成功率: 100.0%
2025-07-29 02:17:25,172 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (342, 384) -> (342, 896), 时长: 0.754秒
2025-07-29 02:17:25,928 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (342, 384) 到 (342, 896), 持续 0.754秒, 成功率: 100.0%
2025-07-29 02:17:27,433 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (175, 896) -> (175, 384), 时长: 0.54秒
2025-07-29 02:17:27,974 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (175, 896) 到 (175, 384), 持续 0.54秒, 成功率: 100.0%
