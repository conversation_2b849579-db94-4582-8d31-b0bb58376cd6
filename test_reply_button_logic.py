#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回复按钮定位逻辑的差异
对比文本追评和表情追评中'回复1'定位的不同实现
"""

def simulate_text_follow_up_logic():
    """模拟文本追评中的回复按钮定位逻辑（interaction_controller.py）"""
    print("=== 文本追评中的回复按钮定位逻辑 ===")
    print("文件：interaction_controller.py")
    print("方法：_send_follow_up_text()")
    print()
    
    # 模拟找到多个匹配的回复按钮
    mock_all_matches = [
        {"center": [290, 950], "x": 290, "y": 950, "score": 0.85},  # 第二个评论的回复按钮
        {"center": [290, 811], "x": 290, "y": 811, "score": 0.82},  # 第一个评论的回复按钮（最上方）
        {"center": [290, 1100], "x": 290, "y": 1100, "score": 0.80}, # 第三个评论的回复按钮
    ]
    
    print("找到的所有匹配项：")
    for i, match in enumerate(mock_all_matches):
        print(f"  匹配{i+1}: 坐标({match['x']}, {match['y']}), Y坐标={match['center'][1]}, 得分={match['score']}")
    
    # 文本追评的正确逻辑：按Y坐标排序，选择最上方的
    print("\n执行排序逻辑：按Y坐标排序，选择最上方的")
    sorted_matches = sorted(mock_all_matches, key=lambda x: x.get("center", [0, 0])[1])
    topmost_match = sorted_matches[0]
    
    print("排序后的匹配项：")
    for i, match in enumerate(sorted_matches):
        marker = " ← 选中（最上方）" if i == 0 else ""
        print(f"  匹配{i+1}: 坐标({match['x']}, {match['y']}), Y坐标={match['center'][1]}{marker}")
    
    print(f"\n✅ 文本追评结果：选择最上方的回复按钮 -> 坐标({topmost_match['x']}, {topmost_match['y']})")
    return topmost_match

def simulate_emoji_follow_up_logic_current():
    """模拟当前表情追评中的回复按钮定位逻辑（account_nurturing_controller.py）"""
    print("\n" + "="*60)
    print("=== 当前表情追评中的回复按钮定位逻辑 ===")
    print("文件：account_nurturing_controller.py")
    print("方法：_send_follow_up_content()")
    print()
    
    # 模拟相同的匹配结果
    mock_all_matches = [
        {"center": [290, 950], "x": 290, "y": 950, "score": 0.85},  # 第二个评论的回复按钮
        {"center": [290, 811], "x": 290, "y": 811, "score": 0.82},  # 第一个评论的回复按钮（最上方）
        {"center": [290, 1100], "x": 290, "y": 1100, "score": 0.80}, # 第三个评论的回复按钮
    ]
    
    print("找到的所有匹配项：")
    for i, match in enumerate(mock_all_matches):
        print(f"  匹配{i+1}: 坐标({match['x']}, {match['y']}), Y坐标={match['center'][1]}, 得分={match['score']}")
    
    # 模拟当前的问题逻辑
    print("\n当前逻辑：")
    print("1. 尝试调用 find_topmost_element_by_template() 方法")
    print("2. 如果方法不存在或失败，回退到标准的 click_element('reply_button_1')")
    print("3. 标准方法只返回第一个找到的匹配项（通常是得分最高的，不是最上方的）")
    
    # 模拟标准方法的行为：返回得分最高的（不是最上方的）
    standard_method_result = max(mock_all_matches, key=lambda x: x['score'])
    
    print(f"\n❌ 当前表情追评结果：选择得分最高的回复按钮 -> 坐标({standard_method_result['x']}, {standard_method_result['y']})")
    print(f"   问题：这不是最上方的按钮！最上方应该是Y坐标=811的按钮")
    
    return standard_method_result

def simulate_emoji_follow_up_logic_fixed():
    """模拟修复后表情追评中的回复按钮定位逻辑"""
    print("\n" + "="*60)
    print("=== 修复后表情追评中的回复按钮定位逻辑 ===")
    print("修复方案：确保表情追评也使用与文本追评相同的最上方选择逻辑")
    print()
    
    # 模拟相同的匹配结果
    mock_all_matches = [
        {"center": [290, 950], "x": 290, "y": 950, "score": 0.85},  # 第二个评论的回复按钮
        {"center": [290, 811], "x": 290, "y": 811, "score": 0.82},  # 第一个评论的回复按钮（最上方）
        {"center": [290, 1100], "x": 290, "y": 1100, "score": 0.80}, # 第三个评论的回复按钮
    ]
    
    print("找到的所有匹配项：")
    for i, match in enumerate(mock_all_matches):
        print(f"  匹配{i+1}: 坐标({match['x']}, {match['y']}), Y坐标={match['center'][1]}, 得分={match['score']}")
    
    # 修复后的逻辑：与文本追评保持一致
    print("\n修复后的逻辑：")
    print("1. 直接使用与文本追评相同的最上方选择逻辑")
    print("2. 按Y坐标排序，选择Y坐标最小的（最上方的）")
    print("3. 确保表情追评和文本追评使用完全相同的定位策略")
    
    # 应用正确的逻辑
    sorted_matches = sorted(mock_all_matches, key=lambda x: x.get("center", [0, 0])[1])
    topmost_match = sorted_matches[0]
    
    print("排序后的匹配项：")
    for i, match in enumerate(sorted_matches):
        marker = " ← 选中（最上方）" if i == 0 else ""
        print(f"  匹配{i+1}: 坐标({match['x']}, {match['y']}), Y坐标={match['center'][1]}{marker}")
    
    print(f"\n✅ 修复后表情追评结果：选择最上方的回复按钮 -> 坐标({topmost_match['x']}, {topmost_match['y']})")
    return topmost_match

def compare_results():
    """对比不同逻辑的结果"""
    print("\n" + "="*80)
    print("📊 结果对比分析")
    print("="*80)
    
    # 执行所有测试
    text_result = simulate_text_follow_up_logic()
    emoji_current_result = simulate_emoji_follow_up_logic_current()
    emoji_fixed_result = simulate_emoji_follow_up_logic_fixed()
    
    print(f"\n对比结果：")
    print(f"文本追评选择的坐标：     ({text_result['x']}, {text_result['y']}) - Y坐标={text_result['center'][1]} ✅")
    print(f"表情追评当前选择的坐标： ({emoji_current_result['x']}, {emoji_current_result['y']}) - Y坐标={emoji_current_result['center'][1]} ❌")
    print(f"表情追评修复后选择的坐标：({emoji_fixed_result['x']}, {emoji_fixed_result['y']}) - Y坐标={emoji_fixed_result['center'][1]} ✅")
    
    # 验证修复是否成功
    text_y = text_result['center'][1]
    emoji_current_y = emoji_current_result['center'][1]
    emoji_fixed_y = emoji_fixed_result['center'][1]
    
    print(f"\n分析：")
    if text_y == emoji_fixed_y:
        print("✅ 修复成功：表情追评修复后与文本追评选择相同的最上方按钮")
    else:
        print("❌ 修复失败：表情追评修复后仍与文本追评选择不同")
    
    if emoji_current_y != text_y:
        print("✅ 问题确认：当前表情追评确实选择了错误的按钮")
    else:
        print("❌ 问题不存在：当前表情追评选择正确")
    
    print(f"\n🎯 结论：")
    print(f"问题根源：表情追评使用标准定位方法，选择得分最高的按钮（Y={emoji_current_y}）")
    print(f"正确做法：应该选择Y坐标最小的按钮（Y={text_y}），即最上方的按钮")
    print(f"修复方案：让表情追评使用与文本追评相同的最上方选择逻辑")

def main():
    """主测试函数"""
    print("回复按钮定位逻辑差异分析")
    print("="*80)
    print("问题：表情追评时不会点击最上方匹配的'回复1'节点")
    print("分析：对比文本追评和表情追评中的定位逻辑差异")
    print("="*80)
    
    compare_results()
    
    print(f"\n" + "="*80)
    print("🔧 修复建议")
    print("="*80)
    print("1. 检查 account_nurturing_controller.py 中的 _send_follow_up_content() 方法")
    print("2. 确保表情追评使用与 interaction_controller.py 相同的最上方选择逻辑")
    print("3. 修复 find_topmost_element_by_template() 方法调用失败的问题")
    print("4. 或者直接实现与文本追评相同的排序选择逻辑")

if __name__ == "__main__":
    main()
