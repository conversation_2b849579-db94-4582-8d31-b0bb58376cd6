#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
养号任务控制器
负责执行养号任务，包括浏览、点赞、评论、收藏、关注等操作
"""

import os
import sys
import time
import random
import json
import traceback
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目根目录到系统路径
project_root = str(Path(__file__).parent.parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 新系统独立导入
from xhs_matrix.core.enhanced_rpc_client import OptimizedMytRpc as XHSDevice
from xhs_matrix.legacy.controllers.base_controller import BaseController
from xhs_matrix.common.logger import logger

# 新系统不需要容器崩溃检测，创建空实现
class ContainerCrashDetector:
    def __init__(self, _config):
        # 忽略config参数，新系统不需要
        pass
    def record_detection_result(self, *_args):
        # 忽略args参数，新系统不需要
        return False
    def start_recovery(self):
        pass
    def finish_recovery(self, _success: bool):
        # 忽略success参数，新系统不需要
        pass
    def get_detection_stats(self):
        return {}

class SplitRestartRecoveryManager:
    def __init__(self, _manager):
        # 忽略manager参数，新系统不需要
        pass

CONTAINER_CRASH_DETECTION_CONFIG = {}

class AccountNurturingController(BaseController):
    """
    养号任务控制器
    负责执行各种养号操作
    """
    
    # 小红书包名
    PACKAGE_NAME = "com.xingin.xhs"
    
    def __init__(self, device: XHSDevice, config_path: Optional[str] = None, config_json_path: Optional[str] = None, **kwargs):
        """
        初始化养号任务控制器

        Args:
            device: 设备对象
            config_path: 元素配置文件路径
            config_json_path: json配置文件路径（可选）
            **kwargs: 其他参数（如task_id, device_ip, device_port等）
        """
        # 将所有参数传递给基类，包括新系统的参数
        super().__init__(device=device, config_path=config_path, **kwargs)
        
        # 养号相关状态
        self.current_page = "unknown"  # 当前所在页面
        self.browse_count = 0          # 浏览笔记数量
        self.like_count = 0            # 点赞数量
        self.comment_count = 0         # 评论数量
        self.collect_count = 0         # 收藏数量
        self.follow_count = 0          # 关注数量
        self.is_terminated = False     # 添加终止标记

        # APP重启相关状态
        self.restart_count = 0         # 当前任务的重启次数
        self.max_restart_count = 3     # 最大重启次数限制

        # 追评功能相关属性
        self.enable_follow_up = False           # 是否启用追评功能

        # 新系统不需要容器崩溃检测功能
        self.crash_detector = ContainerCrashDetector(CONTAINER_CRASH_DETECTION_CONFIG.copy())
        self.split_restart_manager = None
        self.follow_up_templates = []           # 追评文本模板列表
        self.follow_up_duration = 0             # 当前详情页追评操作耗时
        self.total_follow_up_time = 0           # 当前关键词累积的追评时间
        
        # 读取评论模板文件
        self.comment_templates = self._load_comment_templates()
        
        # 读取表情包模板（sticker_templates）
        self.sticker_templates = []
        if config_json_path and os.path.exists(config_json_path):
            try:
                with open(config_json_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.sticker_templates = config_data.get('sticker_templates', [])
            except Exception as e:
                logger.warning(f"加载sticker_templates失败: {e}")
        logger.info(f"[INIT] 当前sticker_templates数量: {len(self.sticker_templates)}")
    
    def get_specific_status(self) -> Dict[str, Any]:
        """
        获取养号任务特定状态
        
        Returns:
            状态字典
        """
        return {
            "current_page": self.current_page,
            "browse_count": self.browse_count,
            "like_count": self.like_count,
            "comment_count": self.comment_count,
            "collect_count": self.collect_count,
            "follow_count": self.follow_count,
            "search_count": 0  # 固定为0，保持接口兼容
        }
    
    def _load_comment_templates(self) -> List[str]:
        """
        加载评论模板
        
        注意：默认模板已弃用，请通过execute方法的comments参数传入评论内容
        
        Returns:
            评论模板列表
        """
        # 返回空列表，由外部参数提供评论模板
        templates = []
        
        # 记录日志
        logger.info("评论模板将由外部参数提供，不再使用默认模板")
        
        return templates
    
    def _get_random_comment(self) -> Optional[str]:
        """
        获取随机评论
        
        Returns:
            随机评论内容，如果评论模板为空返回None
        """
        if not self.comment_templates:
            logger.warning("⚠️ 评论模板为空，无法随机选择评论")
            return None
        return random.choice(self.comment_templates)
    
    def _record_page_detection_success(self, page_type: str):
        """
        🔧 新增：记录页面检测成功到崩溃检测器

        Args:
            page_type: 检测到的页面类型
        """
        if hasattr(self, 'crash_detector') and self.crash_detector:
            self.crash_detector.record_detection_result(True, True, page_type)

    def detect_current_page(self, hint: Optional[str] = None) -> str:
        """
        检测当前页面类型

        Args:
            hint: 提示可能的页面类型，如果提供则优先检查该类型

        Returns:
            页面类型: home, detail, profile, search, author, message, hot, publish, discovery, unknown
        """
        # 使用页面指示器进行更可靠的页面验证
        # 对每种页面类型检查特征元素
        
        # 如果提供了hint，先检查该类型
        if hint:
            logger.info(f"根据提示'{hint}'优先检查该类型")
            if self.verify_page_type(hint):
                logger.info(f"通过快速验证确认当前在{hint}页")
                self._record_page_detection_success(hint)
                return hint
            
            logger.info(f"提示的页面类型'{hint}'验证失败，继续检查其他类型")
        
        # 修改检查顺序，优先检查详情页和具体频道页面

        # 检查是否在详情页
        if self.verify_page_type("detail"):
            logger.info("通过快速验证确认当前在详情页")
            self._record_page_detection_success("detail")
            return "detail"

        # 紧接着检查是否在发现视频页
        if self.verify_page_type("discovery"):
            logger.info("通过快速验证确认当前在发现视频页")
            self._record_page_detection_success("discovery")
            return "discovery"

        # 优先检测具体的首页频道，而不是泛化的首页
        # 检查是否在潮鞋频道
        if self.element_manager and self.element_manager.find_element("channel_shoes_selected"):
            logger.info("✅ 通过频道选中状态确认当前在首页潮鞋频道")
            self._record_page_detection_success("home_shoes")
            return "home_shoes"

        # 检查是否在穿搭频道
        if self.element_manager and self.element_manager.find_element("channel_fashion_selected"):
            logger.info("✅ 通过频道选中状态确认当前在首页穿搭频道")
            self._record_page_detection_success("home_fashion")
            return "home_fashion"

        # 检查是否在推荐频道
        if self.element_manager and self.element_manager.find_element("recommend_tab_selected"):
            logger.info("✅ 通过频道选中状态确认当前在首页推荐频道")
            self._record_page_detection_success("home_recommend")
            return "home_recommend"

        # 最后检查是否在泛化的首页（作为兜底）
        if self.verify_page_type("home"):
            logger.info("通过快速验证确认当前在首页（无法确定具体频道）")
            self._record_page_detection_success("home")
            return "home"
        
        # 检查是否在个人主页
        if self.verify_page_type("profile"):
            logger.info("通过快速验证确认当前在个人主页")
            self._record_page_detection_success("profile")
            return "profile"

        # 检查是否在搜索页
        if self.verify_page_type("search"):
            logger.info("通过快速验证确认当前在搜索页")
            self._record_page_detection_success("search")
            return "search"

        # 检查是否在消息页
        if self.verify_page_type("message"):
            logger.info("通过快速验证确认当前在消息页")
            self._record_page_detection_success("message")
            return "message"

        # 检查是否在发布页
        if self.verify_page_type("publish"):
            logger.info("通过快速验证确认当前在发布页")
            self._record_page_detection_success("publish")
            return "publish"
            
        # 使用标准方式进行更详细的检查
        # 依次检查每种可能的页面类型
        page_types = ['detail', 'discovery', 'home', 'profile', 'search', 'author', 'message', 'hot', 'publish']
        for page_type in page_types:
            if self.element_manager:
                indicators = self.element_manager.find_page_indicators(page_type, min_matches=1)
                if indicators:
                    logger.info(f"通过页面指示器确认当前在{page_type}页")
                    self.current_page = page_type
                    self._record_page_detection_success(page_type)
                    return page_type
        
        # 🔧 新增：容器崩溃检测逻辑
        # 如果所有检测都失败，记录到崩溃检测器
        screenshot_success = True  # takeCaptrueCompress调用成功（能到这里说明截图成功）
        template_match_success = False  # 所有模板匹配都失败

        if hasattr(self, 'crash_detector') and self.crash_detector:
            if self.crash_detector.record_detection_result(screenshot_success, template_match_success, "unknown"):
                logger.error("🚨 检测到容器崩溃，启动分拆式重启恢复")

                # 执行崩溃恢复
                if self._handle_container_crash_with_split_restart():
                    logger.info("✅ 容器崩溃恢复成功，重新检测页面")
                    # 重新检测页面
                    return self.detect_current_page(hint)
                else:
                    logger.error("❌ 容器崩溃恢复失败")
                    self.current_page = "unknown"
                    return "unknown"

        # 如果无法确定，返回unknown
        logger.warning("无法确定当前页面类型，标记为unknown")
        self.current_page = "unknown"
        return "unknown"

    def _detect_home_channel(self) -> str:
        """
        检测首页的具体频道
        仅用于页面状态验证，不进行点击操作

        Returns:
            具体的首页频道类型: home_shoes, home_fashion, home_recommend, home
        """
        try:
            if not self.element_manager:
                logger.warning("⚠️ ElementManager未初始化，无法检测频道状态")
                return "unknown"

            # 检查潮鞋频道选中状态
            if self.element_manager.find_element("channel_shoes_selected"):
                logger.info("✅ 检测到当前在首页潮鞋频道")
                return "home_shoes"

            # 检查穿搭频道选中状态
            elif self.element_manager.find_element("channel_fashion_selected"):
                logger.info("✅ 检测到当前在首页穿搭频道")
                return "home_fashion"

            # 检查推荐频道选中状态
            elif self.element_manager.find_element("recommend_tab_selected"):
                logger.info("✅ 检测到当前在首页推荐频道")
                return "home_recommend"

            # 如果都没有检测到，返回普通首页
            else:
                logger.info("📝 在首页但无法确定具体频道，返回普通首页状态")
                return "home"

        except Exception as e:
            logger.warning(f"⚠️ 检测首页频道时异常: {str(e)}")
            return "home"

    def _check_channel_status(self, expected_channel: str) -> str:
        """
        检查当前频道状态（方案2: 双重检测对比）

        Args:
            expected_channel: 期望的频道类型 ("shoes", "fashion", "home")

        Returns:
            当前检测到的频道状态: "correct", "wrong_channel", "no_channel"
        """
        try:
            # 方案2: 双重检测对比
            # 第一次检测：无hint，获取最准确的页面状态（优先检测具体频道）
            current_page_detailed = self.detect_current_page()  # 无hint
            logger.info(f"📝 详细检测结果: {current_page_detailed}")

            # 第二次检测：有hint="home"，获取泛化检测结果
            current_page_general = self.detect_current_page(hint="home")  # 有hint
            logger.info(f"📝 泛化检测结果: {current_page_general}")

            # 如果检测结果不一致，以具体频道检测为准（用户建议的优先级判断）
            if current_page_detailed != current_page_general:
                logger.info(f"🔍 检测结果不一致，以具体频道为准: {current_page_detailed} (详细) vs {current_page_general} (泛化)")
                current_page = current_page_detailed  # 优先使用详细检测结果
            else:
                logger.info(f"🔍 检测结果一致: {current_page_detailed}")
                current_page = current_page_detailed

            # 🔧 优化频道状态检查逻辑，区分普通首页浏览和具体频道浏览
            if expected_channel == "home":
                # 普通首页浏览：接受所有首页类型页面
                if current_page.startswith("home"):
                    logger.info(f"✅ 普通首页浏览: 在有效首页 {current_page}")
                    return "correct"
                else:
                    logger.warning(f"⚠️ 普通首页浏览: 不在首页 {current_page}")
                    return "no_channel"
            else:
                # 具体频道浏览：需要精确匹配
                if expected_channel == "shoes":
                    expected_page = "home_shoes"
                elif expected_channel == "fashion":
                    expected_page = "home_fashion"
                else:
                    expected_page = "home"

                # 用户建议的优先级判断逻辑：当验证页面出现home以及home_shoes或穿搭频道时，以频道页面的验证为准
                if current_page in ["home_shoes", "home_fashion"] and expected_page in ["home_shoes", "home_fashion"]:
                    # 当检测到具体频道时，以具体频道为准
                    if current_page == expected_page:
                        logger.info(f"✅ 频道状态检查: 在正确的具体频道 {current_page}")
                        return "correct"
                    else:
                        logger.warning(f"⚠️ 频道状态检查: 在具体频道但不匹配 (当前: {current_page}, 期望: {expected_page})")
                        return "wrong_channel"
                elif current_page == expected_page:
                    logger.info(f"✅ 频道状态检查: 在正确频道 {current_page}")
                    return "correct"
                elif current_page.startswith("home"):
                    logger.warning(f"⚠️ 频道状态检查: 在首页但频道不对 (当前: {current_page}, 期望: {expected_page})")
                    return "wrong_channel"
                else:
                    logger.warning(f"⚠️ 频道状态检查: 不在首页 (当前: {current_page})")
                    return "no_channel"

        except Exception as e:
            logger.error(f"❌ 检查频道状态时异常: {str(e)}")
            return "no_channel"

    def _recover_channel_status(self, target_channel: str, max_retries: int = 2) -> bool:
        """
        恢复到正确的频道（增强版，支持重试）

        Args:
            target_channel: 目标频道 ("shoes", "fashion", "home")
            max_retries: 最大重试次数

        Returns:
            是否成功恢复
        """
        logger.info(f"🔄 开始恢复到频道: {target_channel} (最大重试次数: {max_retries})")

        for attempt in range(max_retries + 1):
            try:
                # 🔧 修复停止响应：在频道恢复循环中检查停止标志
                if self.check_stop_requested():
                    logger.info("⚠️ 检测到停止请求，中断频道恢复")
                    return False

                if attempt > 0:
                    logger.info(f"🔄 第 {attempt + 1} 次尝试恢复频道")

                # 步骤1: 确保在首页
                current_page = self.detect_current_page()
                if not current_page.startswith("home"):
                    logger.info("📝 当前不在首页，先回到首页")
                    if not self.go_to_home():
                        logger.warning("⚠️ 无法回到首页，跳过此次恢复尝试")
                        continue

                # 步骤2: 按一次返回键（确保在推荐页）
                logger.info("📝 按一次返回键确保在推荐页")
                self.back()
                if self.interruptible_sleep(2):
                    return False

                # 步骤3: 执行向下滑动操作显示频道标签
                logger.info("📝 执行向下滑动操作显示频道标签")
                if not self._swipe_down_to_show_channels():
                    logger.warning("向下滑动失败，尝试下一次")
                    continue
                if self.interruptible_sleep(1.5):  # 等待页面稳定
                    return False

                # 步骤4: 如果目标是推荐频道，直接验证
                if target_channel == "home":
                    logger.info("✅ 目标是推荐频道，验证当前状态")
                    if self._verify_channel_switch(target_channel):
                        return True
                    else:
                        continue

                # 步骤5: 图像模板定位频道标签进行点击
                template_name = self._get_channel_template_name(target_channel)
                if not template_name:
                    logger.error(f"未知频道类型: {target_channel}")
                    return False

                logger.info(f"📝 查找并点击频道标签: {template_name}")

                # 增加点击重试机制
                click_success = False
                for click_attempt in range(2):
                    # 🔧 修复停止响应：在点击重试循环中检查停止标志
                    if self.check_stop_requested():
                        logger.info("⚠️ 检测到停止请求，中断点击重试")
                        return False

                    if self.element_manager and self.element_manager.click_element(template_name):
                        logger.info(f"✅ 成功点击频道标签: {template_name}")
                        click_success = True
                        break
                    else:
                        logger.warning(f"⚠️ 点击频道标签失败，重试第 {click_attempt + 1} 次")
                        # 🔧 修复停止响应：分段等待，每0.2秒检查一次停止标志
                        for _ in range(5):  # 1秒分成5段
                            if self.check_stop_requested():
                                logger.info("⚠️ 检测到停止请求，中断点击重试等待")
                                return False
                            time.sleep(0.2)

                if not click_success:
                    logger.warning(f"⚠️ 点击频道标签 {template_name} 失败，尝试下一次恢复")
                    continue

                # 步骤6: 等待页面加载并验证
                if self.interruptible_sleep(3):  # 等待页面加载
                    return False

                # 步骤7: 验证是否成功切换到目标频道
                if self._verify_channel_switch(target_channel):
                    logger.info(f"✅ 频道恢复成功: {target_channel}")
                    return True
                else:
                    logger.warning(f"⚠️ 频道切换验证失败，尝试第 {attempt + 1} 次")
                    continue

            except Exception as e:
                logger.error(f"❌ 频道恢复第 {attempt + 1} 次尝试异常: {str(e)}")
                continue

        logger.error(f"❌ 频道恢复失败，已尝试 {max_retries + 1} 次")
        return False

    def _swipe_down_to_show_channels(self) -> bool:
        """执行向下滑动显示频道标签"""
        try:
            width = self.DEFAULT_SCREEN_WIDTH
            height = self.DEFAULT_SCREEN_HEIGHT

            # 在屏幕上方区域向下滑动
            start_x = int(width * 0.5)
            start_y = int(height * 0.2)
            end_x = start_x + random.randint(-30, 30)
            end_y = start_y + int(height * 0.3)  # 向下滑动

            logger.debug(f"向下滑动显示频道标签: ({start_x}, {start_y}) -> ({end_x}, {end_y})")
            return self.swipe_with_bezier(start_x, start_y, end_x, end_y, 0.8)

        except Exception as e:
            logger.error(f"❌ 向下滑动异常: {str(e)}")
            return False

    def _get_channel_template_name(self, channel: str) -> str:
        """获取频道对应的模板名称"""
        channel_mapping = {
            "shoes": "channel_shoes",
            "fashion": "channel_fashion",
            "home": "home_tab"
        }
        return channel_mapping.get(channel, "")

    def _verify_channel_switch(self, target_channel: str, max_wait_time: int = 5) -> bool:
        """
        验证频道切换是否成功（方案2增强版，使用双重检测）

        Args:
            target_channel: 目标频道
            max_wait_time: 最大等待时间（秒）

        Returns:
            是否切换成功
        """
        try:
            expected_pages = {
                "shoes": "home_shoes",
                "fashion": "home_fashion",
                "home": "home"
            }

            expected_page = expected_pages.get(target_channel, "home")
            logger.info(f"📝 验证频道切换: 期望页面 {expected_page}")

            # 等待页面稳定，多次检测
            for attempt in range(max_wait_time):
                # 🔧 修复停止响应：在验证循环中检查停止标志
                if self.check_stop_requested():
                    logger.info("⚠️ 检测到停止请求，中断频道切换验证")
                    return False

                # 方案2: 使用双重检测，移除hint参数，使用最准确的检测
                current_page_detailed = self.detect_current_page()  # 无hint，最准确
                current_page_general = self.detect_current_page(hint="home")  # 有hint，兜底

                # 如果检测结果不一致，以具体频道为准
                if current_page_detailed != current_page_general:
                    logger.info(f"📝 第 {attempt + 1} 次检测结果不一致，以具体频道为准: {current_page_detailed} vs {current_page_general}")
                    current_page = current_page_detailed
                else:
                    current_page = current_page_detailed

                logger.info(f"📝 第 {attempt + 1} 次检测: 当前页面 {current_page}")

                # 用户建议的优先级判断：以具体频道检测为准
                if current_page in ["home_shoes", "home_fashion"] and expected_page in ["home_shoes", "home_fashion"]:
                    # 当检测到具体频道时，以具体频道为准
                    if current_page == expected_page:
                        logger.info(f"✅ 频道切换验证成功: {current_page} (具体频道匹配)")
                        return True
                    else:
                        logger.info(f"📝 在具体频道但不匹配，继续等待: {current_page} (期望: {expected_page})")
                elif current_page == expected_page:
                    logger.info(f"✅ 频道切换验证成功: {current_page}")
                    return True
                elif current_page.startswith("home") and current_page != expected_page:
                    logger.info(f"📝 在首页但频道不对，继续等待: {current_page}")
                else:
                    logger.warning(f"⚠️ 不在首页: {current_page}")

                # 如果不是最后一次尝试，等待1秒（分段等待以便及时响应停止）
                if attempt < max_wait_time - 1:
                    # 🔧 修复停止响应：分段等待，每0.2秒检查一次停止标志
                    for _ in range(5):  # 1秒分成5段，每段0.2秒
                        if self.check_stop_requested():
                            logger.info("⚠️ 检测到停止请求，中断频道切换验证等待")
                            return False
                        time.sleep(0.2)

            # 最终验证失败 - 使用双重检测获取最终状态
            final_page_detailed = self.detect_current_page()
            final_page_general = self.detect_current_page(hint="home")
            final_page = final_page_detailed if final_page_detailed != final_page_general else final_page_detailed

            logger.warning(f"⚠️ 频道切换验证失败: 期望{expected_page}, 最终{final_page} (详细:{final_page_detailed}, 泛化:{final_page_general})")
            return False

        except Exception as e:
            logger.error(f"❌ 验证频道切换时异常: {str(e)}")
            return False

    def _ensure_correct_channel_after_interaction(self) -> bool:
        """
        互动后确保在正确频道（方案2: 双重检测对比）

        Returns:
            是否成功确保在正确频道
        """
        try:
            # 方案2: 双重检测对比
            current_page_detailed = self.detect_current_page()  # 无hint，最准确
            current_page_general = self.detect_current_page(hint="home")  # 有hint，兜底

            # 如果检测结果不一致，以具体频道为准
            if current_page_detailed != current_page_general:
                logger.info(f"📝 互动后页面检测结果不一致，以具体频道为准: {current_page_detailed} (详细) vs {current_page_general} (泛化)")
                current_page = current_page_detailed
            else:
                logger.info(f"📝 互动后页面检测结果一致: {current_page_detailed}")
                current_page = current_page_detailed

            logger.info(f"📝 互动后页面检测结果: {current_page}")

            if current_page == "detail":
                logger.info("✅ 已返回详情页")
                return True
            elif current_page.startswith("home"):
                logger.info(f"✅ 已返回首页: {current_page}")

                # 获取目标频道
                target_channel = getattr(self, '_target_channel', 'home')

                # 检查是否在正确频道（这里会再次使用双重检测）
                channel_status = self._check_channel_status(target_channel)

                if channel_status == "correct":
                    logger.info(f"✅ 已在正确频道: {current_page}")
                    return True
                elif channel_status == "wrong_channel":
                    logger.warning(f"⚠️ 返回首页但不在正确频道，尝试恢复到: {target_channel}")
                    if self._recover_channel_status(target_channel):
                        logger.info(f"✅ 频道恢复成功，现在在: {target_channel}")
                        return True
                    else:
                        logger.warning("⚠️ 频道恢复失败，但继续执行任务")
                        return False
                else:  # no_channel
                    logger.warning(f"⚠️ 不在首页，当前页面: {current_page}")
                    return False
            else:
                logger.warning(f"⚠️ 互动后返回到未预期页面：{current_page}")
                return False

        except Exception as e:
            logger.error(f"❌ 互动后频道状态检查异常: {str(e)}")
            return False

    def _return_from_detail_and_verify(self) -> bool:
        """
        🔧 增强功能：从详情页返回并进行完整验证

        用户建议：应该在从详情页返回时都需要验证，如果验证页面正确再进行滑动，点击进入详情页的操作

        Returns:
            bool: 是否成功返回到正确的首页频道
        """
        logger.info("📝 从详情页返回并验证页面状态")

        # 步骤1: 执行返回操作
        if not self.go_to_home():
            logger.warning("⚠️ go_to_home()失败，尝试使用返回键")
            for _ in range(3):
                self.back()
                time.sleep(1)
                if self.verify_page_type("home"):
                    logger.info("✅ 通过返回键回到首页")
                    break
            else:
                logger.error("❌ 无法返回首页，任务中断")
                return False

        # 步骤2: 必然验证页面状态（无论返回方式如何）
        logger.info("🔍 验证返回后的页面状态")
        current_page = self.detect_current_page()

        if not current_page.startswith("home"):
            logger.error(f"❌ 返回后不在首页，当前页面: {current_page}")
            return False

        # 步骤3: 验证频道状态
        target_channel = getattr(self, '_target_channel', 'home')
        channel_status = self._check_channel_status(target_channel)

        if channel_status == "correct":
            logger.info(f"✅ 返回验证成功，在正确频道: {current_page}")
            self.current_page = current_page  # 同步状态
            return True
        elif channel_status == "wrong_channel":
            logger.warning(f"⚠️ 返回首页但频道不对，尝试恢复到: {target_channel}")
            if self._recover_channel_status(target_channel):
                logger.info(f"✅ 频道恢复成功")
                # 重新获取当前页面状态
                self.current_page = self.detect_current_page()
                return True
            else:
                logger.error(f"❌ 频道恢复失败")
                return False
        else:  # no_channel
            logger.error(f"❌ 返回后页面状态异常: {current_page}")
            return False

    def go_to_home(self) -> bool:
        """
        回到首页

        Returns:
            是否成功
        """
        # 检测当前页面
        current_page = self.detect_current_page()

        # 如果已在首页（包括各种频道），直接返回成功
        if current_page.startswith("home"):
            logger.info(f"当前已在首页: {current_page}")
            return True

        logger.info(f"当前在{current_page}页面，尝试返回首页")

        # 根据当前页面类型选择不同的返回策略
        if current_page == "detail":
            # 在笔记详情页，只按1次返回键回到首页
            logger.info("详情页：按1次返回键回到首页")
            self.back()
            # 🔧 修复停止响应：使用可中断等待替代time.sleep
            if self.interruptible_sleep(2):  # 等待页面加载
                return False

            # 检查是否回到首页
            current_page = self.detect_current_page(hint="home")
            if current_page.startswith("home"):
                logger.info(f"✅ 已从详情页返回首页: {current_page}")
                # 确保点击首页标签，避免停留在其他频道
                return self._ensure_home_tab_selected()
            else:
                logger.warning(f"⚠️ 从详情页返回后不在首页，当前页面: {current_page}")
                return False

        elif current_page == "profile":
            # 在用户个人页，按1次返回键
            logger.info("用户个人页：按1次返回键")
            self.back()
            # 🔧 修复停止响应：使用可中断等待替代time.sleep
            if self.interruptible_sleep(3):
                return False
        elif current_page == "search":
            # 在搜索页，按3次返回键
            logger.info("搜索页：按3次返回键")
            for _ in range(3):
                # 🔧 修复停止响应：在循环中检查停止标志
                if self.check_stop_requested():
                    return False
                self.back()
                # 🔧 修复停止响应：使用可中断等待替代time.sleep
                if self.interruptible_sleep(2):
                    return False
        elif current_page == "author":
            # 在作者主页，按2次返回键
            logger.info("作者主页：按2次返回键")
            for _ in range(2):
                # 🔧 修复停止响应：在循环中检查停止标志
                if self.check_stop_requested():
                    return False
                self.back()
                # 🔧 修复停止响应：使用可中断等待替代time.sleep
                if self.interruptible_sleep(2):
                    return False
        elif current_page == "message" or current_page == "hot" or current_page == "publish" or current_page == "discovery":
            # 在消息页/热门页/发布页/发现视频页，按2次返回键
            logger.info(f"{current_page}页：按2次返回键")
            for _ in range(2):
                # 🔧 修复停止响应：在循环中检查停止标志
                if self.check_stop_requested():
                    return False
                self.back()
                # 🔧 修复停止响应：使用可中断等待替代time.sleep
                if self.interruptible_sleep(1):
                    return False
        elif current_page == "unknown":
            # 未能识别的页面，重启应用
            logger.info("无法识别当前页面，尝试重启应用")
            return self.restart_app()
        else:
            # 其他页面先尝试1次返回
            logger.info(f"其他页面({current_page})：尝试返回")
            self.back()
            time.sleep(1)

        # 检查是否已回到首页
        time.sleep(1)
        current_page = self.detect_current_page(hint="home")
        if current_page.startswith("home"):
            logger.info(f"已成功返回首页: {current_page}")
            return True
        else:
            logger.warning(f"⚠️ 未能返回首页，当前页面: {current_page}")
            return False

    def _ensure_home_tab_selected(self) -> bool:
        """
        确保首页标签被选中，避免停留在其他频道

        Returns:
            是否成功确保在首页
        """
        try:
            if not self.element_manager:
                logger.warning("⚠️ ElementManager未初始化，无法确保在首页")
                return False

            # 首先检查是否已经选中首页标签
            if self.element_manager.find_element("home_tab_selected"):
                logger.info("✅ 首页标签已选中")
                return True

            # 如果没有选中，尝试点击首页标签
            logger.info("📝 首页标签未选中，尝试点击首页标签")
            if self.element_manager.click_element("home_tab"):
                logger.info("✅ 成功点击首页标签")
                time.sleep(2)  # 等待页面切换

                # 再次验证是否成功
                if self.element_manager.find_element("home_tab_selected"):
                    logger.info("✅ 确认首页标签已选中")
                    return True
                else:
                    logger.warning("⚠️ 点击首页标签后仍未选中")
                    return False
            else:
                # 如果点击失败，尝试使用备用坐标点击
                logger.warning("⚠️ 点击首页标签失败，尝试使用备用坐标")
                fallback_pos = [90, 1240]  # 从配置文件中获取的备用坐标
                if self.tap(fallback_pos[0], fallback_pos[1]):
                    logger.info("✅ 使用备用坐标点击首页标签")
                    time.sleep(2)
                    return True
                else:
                    logger.error("❌ 备用坐标点击也失败")
                    return False

        except Exception as e:
            logger.error(f"❌ 确保首页标签选中时异常: {str(e)}")
            return False

    def open_app(self) -> bool:
        """
        打开小红书应用
        
        Returns:
            是否成功
        """
        # 直接使用device打开应用
        if not self.device:
            logger.error("设备对象未初始化")
            return False

        if hasattr(self.device, 'open_app'):
            result = self.device.open_app(self.PACKAGE_NAME)
        else:
            logger.warning("设备不支持open_app方法")
            result = False

        self.record_action("打开小红书应用", result)
        
        if result:
            # 等待应用启动
            if self.interruptible_sleep(5):
                return False
            
            # 检查是否成功进入首页
            if self.detect_current_page() == "home":
                return True
            else:
                # 尝试回到首页
                return self.go_to_home()
        
        return False
    
    def random_swipe_home(self, min_swipes: int = 3, max_swipes: int = 10) -> int:
        """
        在首页随机滑动
        
        Args:
            min_swipes: 最小滑动次数
            max_swipes: 最大滑动次数
            
        Returns:
            实际滑动次数
        """
        # 仅在当前页面未知或不在首页时验证
        if not self.current_page or self.current_page != "home":
            logger.info("验证当前是否在首页...")
            if self.detect_current_page() != "home":
                logger.warning("当前不在首页，尝试回到首页")
                if not self.go_to_home():
                    logger.error("无法回到首页，滑动操作取消")
                    return 0
        
        # 随机滑动次数
        swipes = random.randint(min_swipes, max_swipes)
        swipe_count = 0
        
        for i in range(swipes):
            if self.check_stop_requested():
                break
                
            # 随机滑动速度（毫秒）
            duration = random.randint(500, 1500)
            
            # 执行滑动
            logger.debug(f"准备执行第 {i+1}/{swipes} 次滑动，持续时间: {duration}毫秒")
            
            # 直接使用默认屏幕尺寸
            width = self.DEFAULT_SCREEN_WIDTH
            height = self.DEFAULT_SCREEN_HEIGHT
            
            # 计算滑动起点和终点 - 增加随机性以模拟人为操作
            # 起点在屏幕中下部区域随机位置
            start_x = random.randint(int(width * 0.2), int(width * 0.8))  # 水平方向20%-80%范围内随机
            start_y = random.randint(int(height * 0.6), int(height * 0.9))  # 垂直方向60%-90%范围内随机
            
            # 终点根据起点和滑动距离计算
            # 向上滑动(下滑内容)，滑动距离为屏幕高度的30%-50%
            slide_distance = random.randint(int(height * 0.3), int(height * 0.5))
            end_x = start_x + random.randint(-30, 30)  # 允许轻微的水平偏移
            end_y = start_y - slide_distance  # 向上滑动
            
            # 确保坐标在屏幕范围内
            end_x = max(50, min(width - 50, end_x))  # 水平方向保持在安全区域
            end_y = max(50, end_y)  # 确保终点不会超出屏幕上方
            
            # 首先尝试使用贝塞尔曲线滑动
            result = False
            try:
                # 注意：swipe_with_bezier需要秒为单位，而duration是毫秒
                duration_sec = duration / 1000.0
                logger.info(f"执行贝塞尔曲线滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y}), duration={duration_sec}秒")
                
                # 使用更自然的贝塞尔曲线滑动，增加移动轨迹的随机性
                # 1. 为控制点增加更多随机性，使曲线更自然
                # 2. 滑动持续时间略微随机化，模拟人类操作不精确的特点
                
                # 滑动持续时间随机微调±10%
                natural_duration = duration_sec * random.uniform(0.9, 1.1)
                
                # 水平方向添加轻微随机偏移，模拟手指自然晃动
                bezier_start_x = start_x + random.randint(-15, 15)
                bezier_end_x = end_x + random.randint(-15, 15)
                
                # 确保坐标在屏幕范围内
                bezier_start_x = max(50, min(width - 50, bezier_start_x))
                bezier_end_x = max(50, min(width - 50, bezier_end_x))
                
                # 贝塞尔曲线的控制点会在swipe_with_bezier内部生成
                # 确保贝塞尔曲线滑动使用秒为单位
                result = self.swipe_with_bezier(bezier_start_x, start_y, bezier_end_x, end_y, natural_duration)
                
                if result:
                    swipe_count += 1
                    logger.info(f"✅ 贝塞尔曲线滑动成功: {swipe_count}/{swipes}")
                else:
                    logger.warning("❌ 贝塞尔曲线滑动失败，将尝试普通滑动")
            except Exception as e:
                logger.warning(f"❌ 贝塞尔曲线滑动异常: {str(e)}，将尝试普通滑动")

            # 如果贝塞尔曲线滑动失败，尝试普通滑动
            if not result:
                logger.info(f"尝试普通滑动: ({start_x}, {start_y}) -> ({end_x}, {end_y}), duration={duration}毫秒")
                try:
                    result = self.swipe(start_x, start_y, end_x, end_y, duration)
                    
                    if result:
                        swipe_count += 1
                        logger.info(f"✅ 普通滑动成功: {swipe_count}/{swipes}")
                    else:
                        logger.warning(f"❌ 普通滑动失败，将继续下一次滑动")
                except Exception as e:
                    logger.warning(f"❌ 普通滑动异常: {str(e)}，将继续下一次滑动")
            
            # 随机等待
            wait_time = random.uniform(1.0, 3.0)
            logger.debug(f"随机等待 {wait_time:.1f} 秒")
            if self.random_wait(1.0, 3.0):
                break  # 保留在循环内的break
        
        logger.info(f"完成随机滑动，成功次数: {swipe_count}/{swipes}")
        return swipe_count
    
    def enter_detail_page(self) -> bool:
        """
        点击进入详情页
        
        Returns:
            是否成功进入详情页
        """
        logger.info("尝试点击进入笔记详情页")
        
        # 确保当前在首页
        current_page = self.detect_current_page(hint="home")
        if current_page != "home":
            logger.warning(f"当前不在首页，而是在{current_page}页，尝试回到首页")
            if not self.go_to_home():
                logger.error("无法回到首页，放弃进入详情页")
                return False
                
        # 等待页面加载
        time.sleep(1)
        
        # 使用固定的屏幕尺寸
        width, height = 720, 1280
        logger.info(f"使用固定屏幕尺寸: {width}x{height}")
        
        # 随机选择点击位置（避开顶部和底部的导航栏区域）
        # 横向位置：屏幕中心附近
        # 纵向位置：屏幕中间偏上区域
        x = random.randint(int(width * 0.3), int(width * 0.7))
        y = random.randint(int(height * 0.3), int(height * 0.6))
        
        try:
            # 使用设备的点击功能
            logger.debug("执行点击操作")
            
            # 使用tap方法直接点击
            logger.debug(f"使用tap方法点击坐标: ({x}, {y})")
            result = self.tap(x, y)
            
            if result:
                logger.info(f"✅ 点击屏幕位置成功: ({x}, {y})")
                self.record_action(f"点击屏幕位置: ({x}, {y})")
            else:
                logger.error(f"❌ 点击屏幕位置失败: ({x}, {y})")
                self.record_action(f"点击屏幕位置失败: ({x}, {y})", False)
                return False
            
            # 等待页面加载
            logger.info("📝 点击后等待页面加载 (3秒)")
            if self.interruptible_sleep(3):
                return False
            
            # 简化验证流程：只检查是否是详情页或发现视频页
            logger.info("检查页面状态: 只验证是否为详情页或发现视频页")
            
            # 先检查是否是详情页
            if self.verify_page_type("detail"):
                self.browse_count += 1
                logger.info(f"浏览计数: {self.browse_count}")
                return True
            
            # 再检查是否是发现视频页
            if self.verify_page_type("discovery"):
                # 检测到是发现视频页，等待5-8秒后返回
                wait_time = random.uniform(5.0, 8.0)
                logger.info(f"⚠️ 检测到进入发现视频页而非详情页，等待 {wait_time:.1f} 秒后返回")
                time.sleep(wait_time)
                
                # 按返回键
                logger.info("按返回键回到主页")
                self.back()
                time.sleep(2)
                
                # 检查是否回到主页
                if self.verify_page_type("home"):
                    logger.info("✅ 成功从发现视频页返回主页")
                else:
                    logger.warning("⚠️ 返回主页失败，尝试使用go_to_home方法")
                    # 尝试使用go_to_home方法
                    self.go_to_home()
                
                return False
            
            # 如果既不是详情页也不是发现视频页，直接返回首页
            logger.warning(f"⚠️ 未能进入详情页或发现视频页，返回首页")
            self.back()
            time.sleep(1)
            
            # 确保回到首页
            if not self.verify_page_type("home"):
                logger.warning("⚠️ 返回首页失败，再次尝试返回")
                # 连续返回几次，增加返回到首页的机会
                for _ in range(2):
                    self.back()
                    time.sleep(1.5)
                    if self.verify_page_type("home"):
                        break
            
            return False
        except Exception as e:
            logger.error(f"❌ 点击进入详情页异常: {str(e)}", exc_info=True)
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            self.record_action("点击进入详情页", False)
            return False
    
    def like_post(self) -> bool:
        """
        点赞笔记
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            return False
        
        # 查找点赞按钮
        result = self.click_element("like_button")
        if result:
            self.like_count += 1
        
        return result
    
    def collect_post(self) -> bool:
        """
        收藏笔记
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            return False
        
        # 查找收藏按钮
        result = self.click_element("collect_button")
        if result:
            self.collect_count += 1
            # 等待收藏完成
            time.sleep(1)
        
        return result
    
    def follow_author(self) -> bool:
        """
        关注作者
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            return False
        
        # 直接在详情页点击关注按钮
        result = self.click_element("follow_button")
        if result:
            self.follow_count += 1
            # 等待关注完成
            time.sleep(1)
        
        return result
    
    def comment_post(self, comment_text: Optional[str] = None, return_after_comment: bool = True, comment_mode: str = "text", sticker_id: Optional[int] = None) -> bool:
        """
        评论笔记
        
        Args:
            comment_text: 评论内容，如果不提供则使用评论模板中的内容
            return_after_comment: 是否在评论后返回详情页
            comment_mode: "text" | "sticker" | "auto"
            sticker_id: 指定表情包id（可选）
        
        Returns:
            是否成功
        """
        # 确保在详情页
        if self.detect_current_page(hint="detail") != "detail":
            return False
        
        # 准备评论文本
        if comment_mode == "auto":
            # 自动模式逻辑改进
            # 如果没有评论文本，检查是否有评论模板
            if comment_text is None and self.comment_templates:
                # 有评论模板时尝试获取随机评论文本
                comment_text = self._get_random_comment()
                
            # 根据是否有评论文本决定使用文本评论还是表情包评论
            if comment_text is not None:
                logger.info("自动模式选择文本评论 (有评论内容)")
                comment_mode = "text"
            else:
                # 无评论文本时使用表情包评论
                logger.info("自动模式选择表情包评论 (无评论内容)")
                comment_mode = "sticker"
        
        # 处理不同类型的评论
        if comment_mode == "sticker":
            try:
                # === 新增：表情评论前先进行5次向下滑动 ===
                logger.info("📝 表情评论前先进行5次向下滑动浏览")
                self._perform_pre_comment_swipes()

                # 点击评论按钮已在外部完成，这里直接点击评论输入框
                if not self.click_element("comment_input"):
                    self.back()
                    return False
                time.sleep(1.5)
                # 1. 点击表情按钮（打开表情面板）
                if not self.click_element("sticker_panel_button"):
                    logger.warning("点击表情按钮失败")
                    if return_after_comment:
                        self.back()
                    return False
                time.sleep(1)
                # 2. 点击表情包按钮（切换到表情包分组/标签页）
                if not self.click_element("sticker_tab_button"):
                    logger.warning("点击表情包按钮失败")
                    if return_after_comment:
                        self.back()
                    return False
                time.sleep(1.5)
                # 3. 选择表情包
                sticker = None
                if sticker_id is not None:
                    sticker = next((s for s in self.sticker_templates if s.get("id") == sticker_id), None)
                if not sticker:
                    if not self.sticker_templates:
                        logger.warning("表情包模板为空，无法评论")
                        if return_after_comment:
                            self.back()
                        return False
                    sticker = random.choice(self.sticker_templates)
                sticker_element = sticker.get("element")
                if not self.click_element(sticker_element):
                    logger.warning(f"点击表情包{sticker_element}失败")
                    if return_after_comment:
                        self.back()
                    return False
                time.sleep(1)
                # 4. 点击发送按钮
                if self.click_element("comment_send_button"):
                    self.comment_count += 1
                    time.sleep(2)

                    # === 新增：追评功能 ===
                    follow_up_success = False
                    logger.info(f"🔍 追评功能检查: enable_follow_up={self.enable_follow_up}, follow_up_type={getattr(self, 'follow_up_type', 'sticker')}, follow_up_templates数量={len(self.follow_up_templates) if self.follow_up_templates else 0}")

                    # 修复追评触发条件：支持表情追评
                    should_execute_follow_up = False
                    if self.enable_follow_up:
                        if hasattr(self, 'follow_up_type') and self.follow_up_type == "sticker":
                            # 表情追评：检查表情包模板是否可用
                            if self.sticker_templates:
                                should_execute_follow_up = True
                                logger.info("✅ 表情追评条件满足：追评功能已启用，表情包模板可用")
                            else:
                                logger.info("⚠️ 表情追评条件不满足：表情包模板为空")
                        else:
                            # 文本追评：检查文本模板是否可用
                            if self.follow_up_templates:
                                should_execute_follow_up = True
                                logger.info("✅ 文本追评条件满足：追评功能已启用，文本模板可用")
                            else:
                                logger.info("⚠️ 文本追评条件不满足：文本模板为空")
                    else:
                        logger.info("⚠️ 追评功能未启用，跳过追评操作")

                    if should_execute_follow_up:
                        logger.info("🔄 开始执行追评操作")
                        follow_up_success = self._execute_follow_up_comment()

                    if return_after_comment:
                        for _ in range(2):
                            self.back()
                            time.sleep(1)

                    if follow_up_success:
                        logger.info("✅ 表情包评论和追评都成功")
                    else:
                        logger.info("✅ 表情包评论成功")
                    return True
                else:
                    logger.warning("点击发送按钮失败")
                    if return_after_comment:
                        self.back()
                    return False
            except Exception as e:
                logger.error(f"表情包评论异常: {str(e)}")
                self.record_action("表情包评论", False)
                if return_after_comment:
                    self.back()
                return False
        else:
            # 文本评论逻辑
            if comment_text is None:
                comment_text = self._get_random_comment()
            if comment_text is None:
                logger.warning("无法评论：评论内容为空")
                return False
            try:
                if not self.click_element("comment_input"):
                    self.back()
                    return False
                time.sleep(1)
                if not self.input_text(comment_text):
                    self.back()
                    self.back()
                    return False
                if self.click_element("comment_send_button"):
                    self.comment_count += 1
                    time.sleep(2)
                    for _ in range(2):
                        self.back()
                        time.sleep(1)
                    return True
                else:
                    self.back()
                    self.back()
                    return False
            except Exception as e:
                logger.error(f"评论异常: {str(e)}")
                self.record_action("评论", False)
                self.back()
                self.back()
            return False
    
    def interact_with_post(self,
                         like_probability: float = 0.7,
                         collect_probability: float = 0.3,
                         comment_probability: float = 0.2,
                         follow_probability: float = 0.1,
                         enable_comment: bool = False,
                         comment_mode: str = "text",
                         sticker_id: Optional[int] = None,
                         # 新增强制执行参数，避免双重概率判断
                         force_like: Optional[bool] = None,
                         force_collect: Optional[bool] = None,
                         force_comment: Optional[bool] = None,
                         force_follow: Optional[bool] = None) -> Dict[str, bool]:
        """
        与当前笔记互动
        
        Args:
            like_probability: 点赞概率
            collect_probability: 收藏概率
            comment_probability: 评论概率
            follow_probability: 关注概率
            enable_comment: 是否启用评论功能
            comment_mode: 评论类型
            sticker_id: 表情包id
        
        Returns:
            互动结果
        """
        # 确保在详情页，使用hint参数优先检查详情页
        if self.detect_current_page(hint="detail") != "detail":
            logger.warning("⚠️ 当前不在详情页，无法进行互动")
            return {
                "like": False,
                "collect": False,
                "comment": False,
                "follow": False
            }
        
        result = {
            "like": False,
            "collect": False,
            "comment": False,
            "follow": False
        }
        
        # 判断是否使用强制执行模式（避免双重概率判断）
        if any(param is not None for param in [force_like, force_collect, force_comment, force_follow]):
            # 使用强制执行模式 - 直接使用预先判断的结果
            like = force_like if force_like is not None else False
            collect = force_collect if force_collect is not None else False
            comment = force_comment if force_comment is not None else False
            follow = force_follow if force_follow is not None else False

            logger.info(f"📊 使用强制执行模式（避免双重概率判断）:")
            logger.info(f"   点赞: {'执行' if like else '跳过'}")
            logger.info(f"   收藏: {'执行' if collect else '跳过'}")
            logger.info(f"   评论: {'执行' if comment else '跳过'}")
            logger.info(f"   关注: {'执行' if follow else '跳过'}")
        else:
            # 使用传统概率判断模式（用于InteractionController等其他调用）
            like_random = random.random()
            collect_random = random.random()
            comment_random = random.random()
            follow_random = random.random()

            # 独立判断每个操作是否需要执行
            like = like_random < like_probability
            collect = collect_random < collect_probability

            # 详细记录评论功能的状态
            logger.info(f"评论功能状态: enable_comment={enable_comment}, comment_probability={comment_probability}")

            # 只有在启用评论功能且概率满足时才评论
            comment = enable_comment and comment_random < comment_probability

            # 计算关注操作
            follow = follow_random < follow_probability

            # 记录详细的概率判断过程
            logger.info(f"📊 interact_with_post 传统概率判断详情:")
            logger.info(f"   点赞: 随机值={like_random:.3f}, 概率阈值={like_probability:.3f}, 结果={'执行' if like else '跳过'}")
            logger.info(f"   收藏: 随机值={collect_random:.3f}, 概率阈值={collect_probability:.3f}, 结果={'执行' if collect else '跳过'}")
            logger.info(f"   评论: 随机值={comment_random:.3f}, 概率阈值={comment_probability:.3f}, 启用={enable_comment}, 结果={'执行' if comment else '跳过'}")
            logger.info(f"   关注: 随机值={follow_random:.3f}, 概率阈值={follow_probability:.3f}, 结果={'执行' if follow else '跳过'}")

            # 记录评论决策结果
            if not enable_comment:
                logger.info("⚠️ 评论功能未启用，跳过评论操作")
            elif not comment:
                logger.info(f"⚠️ 评论概率检查未通过：当前概率={comment_probability}")
            else:
                logger.info(f"✅ 评论功能已启用，将执行评论操作，评论模式={comment_mode}")
        
        # 记录计划的互动操作
        planned_actions = []
        if like:
            planned_actions.append("点赞")
        if collect:
            planned_actions.append("收藏")
        if comment:
            planned_actions.append("评论")
        if follow:
            planned_actions.append("关注")
            
        if planned_actions:
            logger.info(f"📝 详情页计划互动操作: {', '.join(planned_actions)}")
        else:
            logger.info("📝 详情页未计划任何互动操作")
        
        # 执行互动操作
        if like:
            logger.info("📝 尝试点赞笔记")
            result["like"] = self.like_post()
            if result["like"]:
                logger.info("✅ 点赞成功")
            else:
                logger.warning("⚠️ 点赞失败")
            # 等待一下再进行下一个操作
            self.random_wait(0.5, 1.2)
        
        if collect and not self.check_stop_requested():
            logger.info("📝 尝试收藏笔记")
            result["collect"] = self.collect_post()
            if result["collect"]:
                logger.info("✅ 收藏成功")
            else:
                logger.warning("⚠️ 收藏失败")
            self.random_wait(0.5, 1.2)
        
        if comment and not self.check_stop_requested():
            logger.info("📝 尝试评论笔记")
            
            # 修复随机评论模式 - 修改后的逻辑
            if comment_mode == "auto":
                # 先选择评论类型，确保日志清晰记录选择结果
                chosen_mode = random.choice(["text", "sticker"])
                logger.info(f"随机评论模式已选择: {chosen_mode}评论")
                # 如果选择了文本评论，确保有评论文本
                if chosen_mode == "text":
                    comment_text = self._get_random_comment()
                    if comment_text is None:
                        logger.warning("文本评论内容为空，自动切换到表情包评论")
                        chosen_mode = "sticker"
                # 使用最终决定的评论模式
                result["comment"] = self.comment_post(comment_text if chosen_mode == "text" else None, 
                                                    return_after_comment=False, 
                                                    comment_mode=chosen_mode, 
                                                    sticker_id=sticker_id)
            else:
                # 原有的文本评论或表情包评论逻辑
                comment_text = self._get_random_comment() if comment_mode == "text" else None
                result["comment"] = self.comment_post(comment_text, 
                                                    return_after_comment=False, 
                                                    comment_mode=comment_mode, 
                                                    sticker_id=sticker_id)
                
            if result["comment"]:
                logger.info("✅ 评论成功")
            else:
                logger.warning("⚠️ 评论失败")
            self.random_wait(0.5, 1.2)
        
        if follow and not self.check_stop_requested():
            logger.info("📝 尝试关注作者")
            result["follow"] = self.follow_author()
            if result["follow"]:
                logger.info("✅ 关注成功")
            else:
                logger.warning("⚠️ 关注失败")
            self.random_wait(0.5, 1.2)
        
        # 总结互动结果
        success_actions = [action for action, success in result.items() if success]
        if success_actions:
            logger.info(f"✅ 互动总结: 成功完成 {len(success_actions)}/{len(planned_actions)} 个互动操作")
        
        # 🔧 增强功能：互动完成后统一使用返回验证方法
        logger.info("📝 互动完成，执行返回验证")
        if not self._return_from_detail_and_verify():
            logger.error("❌ 互动后返回验证失败")
            result["page_error"] = True
            return result

        return result
    
    def browse_and_interact(self,
                          channel_type: str = "home",  # 新增频道类型参数
                          duration_minutes: float = 10.0,
                          max_posts: int = 20,
                          like_probability: float = 0.7,
                          collect_probability: float = 0.3,
                          comment_probability: float = 0.2,
                          follow_probability: float = 0.1,
                          min_view_time: float = 5.0,
                          max_view_time: float = 20.0,
                          min_operation_interval: float = 2.0,  # 新增：最小操作间隔
                          max_operation_interval: float = 5.0,  # 新增：最大操作间隔
                          enable_comment: bool = False,
                          comment_mode: str = "text",
                          sticker_id: Optional[int] = None,
                          sticker_templates_path: Optional[str] = None,  # 新增：表情包模板路径
                          config_json_path: Optional[str] = None,  # 新增：配置文件路径参数
                          auto_close_app: bool = True) -> Dict[str, Any]:
        """
        浏览并互动

        Args:
            channel_type: 频道类型 (home/shoes/beauty等)
            duration_minutes: 浏览时长(分钟)
            max_posts: 最大浏览笔记数
            like_probability: 点赞概率
            collect_probability: 收藏概率
            comment_probability: 评论概率
            follow_probability: 关注概率
            min_view_time: 最小查看时间(秒)
            max_view_time: 最大查看时间(秒)
            min_operation_interval: 最小操作间隔(秒)
            max_operation_interval: 最大操作间隔(秒)
            enable_comment: 是否启用评论功能
            comment_mode: 评论类型 (text/sticker)
            sticker_id: 表情包id
            sticker_templates_path: 表情包模板路径
            config_json_path: 配置文件路径（用于加载表情包模板）
            auto_close_app: 是否自动关闭APP

        Returns:
            浏览互动结果
        """
        try:
            logger.info("🔍 browse_and_interact 方法开始执行...")

            # 检查是否已标记为终止状态
            if self.is_terminated:
                logger.info("控制器已经被标记为终止状态，不执行浏览互动任务")
                return {"status": "terminated", "message": "任务已被终止"}

            # 常规检查
            logger.info("🔍 开始检查是否请求停止任务...")
            stop_requested = self.check_stop_requested()
            logger.info(f"🔍 停止请求检查结果: {stop_requested}")
            if stop_requested:
                logger.info("检测到停止请求，不执行浏览互动任务")
                return {"status": "stopped", "message": "检测到停止请求"}
        except Exception as e:
            logger.error(f"❌ browse_and_interact 方法初始化异常: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": f"browse_and_interact 初始化异常: {str(e)}"}
            
        # 记录开始执行浏览任务
        logger.info("========== 开始执行浏览互动任务 ==========")
        logger.info(f"📝 任务计划: 浏览时长 {duration_minutes}分钟, 最大浏览 {max_posts}个笔记")
        logger.info(f"📝 互动概率: 点赞 {like_probability*100:.0f}%, 收藏 {collect_probability*100:.0f}%, " +
                   f"评论 {comment_probability*100:.0f}%, 关注 {follow_probability*100:.0f}%")
        logger.info(f"📝 查看时间: {min_view_time}秒 ~ {max_view_time}秒")
        logger.info(f"📝 操作间隔: {min_operation_interval}秒 ~ {max_operation_interval}秒")
        if sticker_templates_path:
            logger.info(f"📝 表情包模板路径: {sticker_templates_path}")
        
        # 确保在首页
        if not self.go_to_home():
            logger.error("❌ 无法进入首页，浏览任务终止")
            return {"status": "error", "message": "无法进入首页"}
        
        # 记录开始时间
        start_time = time.time()
        end_time = start_time + duration_minutes * 60

        # 重置追评时间累积
        self.total_follow_up_time = 0
        
        # 浏览统计
        total_posts = 0
        interactions = {
            "like": 0,
            "collect": 0,
            "comment": 0,
            "follow": 0
        }
        
        # 主浏览循环
        while total_posts < max_posts:
            # 新增终止检查
            device_terminated = False
            if self.device:
                device_terminated = (hasattr(self.device, 'myt') and
                                   getattr(self.device, 'myt', None) and
                                   hasattr(getattr(self.device, 'myt', None), '_handle') and
                                   getattr(getattr(self.device, 'myt', None), '_handle', 1) == 0)

            if self.is_terminated or device_terminated:
                logger.info("设备已终止或句柄已无效，中断浏览任务")
                break
                
            # 计算时间（排除追评时间）
            total_elapsed = time.time() - start_time
            effective_duration = (total_elapsed - self.total_follow_up_time) / 60  # 有效时长(分钟)
            remaining_time = end_time - time.time()

            # 检查有效时长是否达到限制
            if effective_duration >= duration_minutes:
                logger.info(f"任务已达到设定时长 {duration_minutes:.2f} 分钟（有效时长，已排除 {self.total_follow_up_time:.1f} 秒追评时间），停止继续互动")
                break
            
            # 验证当前是否在首页，只在需要时进行验证
            if self.current_page != "home":
                # 新增终止检查
                if self.is_terminated:
                    logger.info("控制器已终止，中断页面验证")
                    break
                    
                # 检查频道状态
                target_channel = getattr(self, '_target_channel', 'home')
                channel_status = self._check_channel_status(target_channel)

                if channel_status == "correct":
                    # 在正确频道，继续执行
                    pass
                elif channel_status == "wrong_channel":
                    # 在首页但频道不对，尝试恢复频道
                    logger.warning(f"⚠️ 在首页但不在正确频道，尝试恢复到: {target_channel}")
                    if not self._recover_channel_status(target_channel):
                        logger.error("❌ 无法恢复到正确频道，任务中断")
                        break
                elif channel_status == "no_channel":
                    # 不在首页，尝试回到首页
                    logger.warning(f"⚠️ 当前不在首页，尝试回到首页")

                    # 新增终止检查
                    if self.is_terminated:
                        logger.info("控制器已终止，中断回到首页操作")
                        break

                    if not self.go_to_home():
                        logger.warning("⚠️ 无法回到首页，尝试重启APP恢复")
                        if self._restart_app_and_recover_channel(channel_type):
                            logger.info("✅ APP重启成功，已恢复到目标频道，继续任务")
                            continue
                        else:
                            logger.error("❌ APP重启失败，任务中断")
                            break
            
            logger.info(f"✅ 当前在首页, 已浏览 {total_posts}/{max_posts} 个笔记, 剩余时间 {remaining_time/60:.1f}分钟")

            # 智能频道切换逻辑 - 基于实际频道状态判断
            if channel_type != "home":
                # 检查当前频道状态
                current_channel_status = self._check_channel_status(channel_type)

                if current_channel_status == "wrong_channel":
                    # 在首页但频道不对，需要切换
                    logger.info(f"🔄 检测到频道不匹配，准备切换到频道: {channel_type}")
                    if not self._switch_to_channel(channel_type):
                        logger.warning(f"⚠️ 切换到频道 {channel_type} 失败，继续使用当前页面内容")
                        # 不中断任务，继续使用当前页面内容
                    else:
                        logger.info(f"✅ 已成功切换到频道: {channel_type}")
                elif current_channel_status == "correct":
                    # 已在正确频道，无需切换
                    logger.info(f"✅ 已在正确频道: {channel_type}")
                # 如果是"no_channel"状态，说明不在首页，会在前面的逻辑中处理

            if self.check_stop_requested():
                logger.info("⚠️ 检测到停止请求，中断浏览任务")
                break
            
            # 随机滑动1-3次
            swipe_count = random.randint(1, 3)
            logger.info(f"📝 计划滑动 {swipe_count} 次后进入详情页")
            
            actual_swipes = self.random_swipe_home(1, 3)
            logger.info(f"✅ 在首页成功滑动 {actual_swipes} 次")
            
            if self.check_stop_requested():
                logger.info("⚠️ 检测到停止请求，中断浏览任务")
                break
            
            # 尝试进入详情页 - 这里已经知道在首页，不需要再次验证
            logger.info("📝 尝试进入详情页...")
            if self.enter_detail_page():
                total_posts += 1
                logger.info(f"✅ 成功进入详情页 (第 {total_posts}/{max_posts} 个笔记)")
                
                # 随机浏览时间
                view_time = random.uniform(min_view_time, max_view_time)
                logger.info(f"📝 计划在详情页浏览 {view_time:.1f} 秒")
                
                # 先在详情页上方区域进行水平右滑(向左滑动)
                horizontal_swipe_times = random.randint(1, 3)
                logger.info(f"📝 计划在详情页上方区域先进行 {horizontal_swipe_times} 次水平右滑")
                
                horizontal_actual_swipes = 0
                for i in range(horizontal_swipe_times):
                    if self.check_stop_requested():
                        logger.info("⚠️ 检测到停止请求，中断详情页浏览")
                        break
                    
                    # 添加页面稳定性检查
                    if i > 0:  # 第一次滑动前不检查
                        # 确保页面已稳定，避免在页面加载过程中滑动
                        logger.info("进行页面稳定性检查...")
                        self.wait(1.0)  # 短暂等待
                        current_page = self.detect_current_page(hint="detail")
                        if current_page != "detail":
                            logger.warning(f"⚠️ 当前不在详情页(检测到: {current_page})，尝试恢复...")
                            if not self.go_to_home():
                                logger.error("❌ 无法恢复正常页面，跳过当前笔记")
                                break
                            logger.info("已回到首页，跳过当前笔记")
                            break
                    
                    # 在屏幕上方区域进行水平右滑(从右往左滑)
                    width = self.DEFAULT_SCREEN_WIDTH
                    height = self.DEFAULT_SCREEN_HEIGHT
                    
                    # 在屏幕0.2至0.3高度位置进行水平滑动
                    swipe_y = int(height * random.uniform(0.2, 0.3))
                    start_x = int(width * 0.8)  # 从屏幕右侧开始
                    end_x = int(width * 0.2)    # 滑动到屏幕左侧
                    duration = random.randint(800, 1200) / 1000.0  # 直接转换为秒
                    
                    logger.debug(f"详情页上方区域水平右滑: ({start_x}, {swipe_y}) -> ({end_x}, {swipe_y}), duration={duration}秒")
                    
                    # 使用贝塞尔曲线滑动，更自然的滑动轨迹
                    try:
                        # 添加人类操作特征 - Y方向的轻微偏移
                        bezier_start_y = swipe_y + random.randint(-40, 40)
                        bezier_end_y = swipe_y + random.randint(-40, 40)
                        
                        # 确保Y轴坐标在合理范围内
                        bezier_start_y = max(50, min(height - 150, bezier_start_y))
                        bezier_end_y = max(50, min(height - 150, bezier_end_y))
                        
                        # 随机微调持续时间，增加自然感
                        natural_duration = duration * random.uniform(0.8, 1.2)
                        
                        # 执行贝塞尔曲线滑动
                        result = self.swipe_with_bezier(start_x, bezier_start_y, end_x, bezier_end_y, natural_duration)
                        
                        if result:
                            horizontal_actual_swipes += 1
                            logger.info(f"✅ 详情页水平右滑成功: {horizontal_actual_swipes}/{horizontal_swipe_times}")
                        else:
                            logger.warning("❌ 详情页水平右滑失败")
                    except Exception as e:
                        logger.warning(f"❌ 详情页水平右滑异常: {str(e)}")
                    
                    # 随机等待
                    self.random_wait(0.8, 1.5)
                
                # 修复概率判断逻辑 - 预先生成所有随机数，避免多重调用
                like_random = random.random()
                collect_random = random.random()
                comment_random = random.random()
                follow_random = random.random()

                # 独立判断每个操作是否需要执行
                should_like = like_random < like_probability
                should_collect = collect_random < collect_probability
                should_comment = enable_comment and comment_random < comment_probability
                should_follow = follow_random < follow_probability

                # 记录概率判断详情
                logger.info(f"📊 概率判断详情:")
                logger.info(f"   点赞: 随机值={like_random:.3f}, 概率阈值={like_probability:.3f}, 结果={'执行' if should_like else '跳过'}")
                logger.info(f"   收藏: 随机值={collect_random:.3f}, 概率阈值={collect_probability:.3f}, 结果={'执行' if should_collect else '跳过'}")
                logger.info(f"   评论: 随机值={comment_random:.3f}, 概率阈值={comment_probability:.3f}, 启用={enable_comment}, 结果={'执行' if should_comment else '跳过'}")
                logger.info(f"   关注: 随机值={follow_random:.3f}, 概率阈值={follow_probability:.3f}, 结果={'执行' if should_follow else '跳过'}")

                # 判断是否需要进行任何互动
                if should_like or should_collect or should_comment or should_follow:
                    planned_interactions = []
                    if should_like: planned_interactions.append("点赞")
                    if should_collect: planned_interactions.append("收藏")
                    if should_comment: planned_interactions.append("评论")
                    if should_follow: planned_interactions.append("关注")

                    logger.info(f"准备与当前笔记互动，计划操作: {', '.join(planned_interactions)}")
                    
                    # 模拟阅读 - 使用滑动浏览替代静置等待
                    read_time = min(max_view_time, view_time * 0.6)  # 确保先阅读一段时间
                    if self.detail_page_browse_with_scrolling(read_time):
                        break
                    
                    # 执行互动 - 传递预先判断的结果，避免双重概率判断
                    interaction_result = self.interact_with_post(
                        # 传递预先判断的结果而不是概率值
                        force_like=should_like,
                        force_collect=should_collect,
                        force_comment=should_comment,
                        force_follow=should_follow,
                        enable_comment=enable_comment,
                        comment_mode=comment_mode,
                        sticker_id=sticker_id
                    )
                    
                    # 检查是否有页面错误
                    if interaction_result.get("page_error", False):
                        logger.warning("⚠️ 互动后页面状态异常，尝试重启APP恢复")
                        if self._restart_app_and_recover_channel(channel_type):
                            logger.info("✅ APP重启成功，已恢复到目标频道，继续任务")
                            continue
                        else:
                            logger.error("❌ APP重启失败，任务中断")
                            break

                    # 更新互动统计
                    for action, success in interaction_result.items():
                        if success and action != "page_error":  # 排除page_error标记
                            interactions[action] += 1

                    # 互动后继续浏览 - 使用滑动浏览替代静置等待
                    remain_view_time = view_time - read_time
                    if remain_view_time > 0:
                        if self.detail_page_browse_with_scrolling(remain_view_time, is_post_interaction=True):
                            break
                else:
                    # 只浏览不互动 - 使用滑动浏览替代静置等待
                    logger.info(f"📝 仅浏览不互动 {view_time:.1f} 秒")
                    if self.detail_page_browse_with_scrolling(view_time):
                        break

                # 🆕 增强功能：使用统一的返回验证方法
                if not self._return_from_detail_and_verify():
                    logger.error("❌ 从详情页返回验证失败，任务中断")
                    break

                # 🆕 操作间隔控制：在完成一个笔记的浏览和互动后等待
                operation_interval = random.uniform(min_operation_interval, max_operation_interval)
                logger.info(f"⏳ 操作间隔等待: {operation_interval:.1f}秒")
                time.sleep(operation_interval)
            else:
                # 点击进入详情页失败
                logger.warning("❌ 无法进入详情页，尝试下一次点击")
                # 确保我们仍然在首页
                if not self.verify_page_type("home"):
                    logger.warning("⚠️ 当前不在首页，尝试恢复")
                    if not self.go_to_home():
                        logger.warning("⚠️ 无法回到首页，尝试重启APP恢复")
                        if self._restart_app_and_recover_channel(channel_type):
                            logger.info("✅ APP重启成功，已恢复到目标频道，继续任务")
                            continue
                        else:
                            logger.error("❌ APP重启失败，任务中断")
                            break
            
            # 随机等待
            wait_time = random.uniform(1.0, 2.0)
            logger.debug(f"随机等待 {wait_time:.1f} 秒")
            if self.random_wait(1.0, 2.0):
                break
        
            # 计算剩余时间
            remaining_time = end_time - time.time()
            
            # 检查是否已达到任务时长上限
            if remaining_time <= 0:
                logger.info(f"✅ 已达到任务时长上限 {duration_minutes} 分钟，停止浏览")
                # 如果auto_close_app为True，则在达到时长限制时关闭应用
                if auto_close_app:
                    logger.info("达到任务时长限制，准备关闭小红书APP")
                    self.close_app()
                break
            
        # 确保回到首页
        if self.go_to_home():
            logger.info("✅ 已确保回到首页")
        
        # 计算总结果
        total_duration = (time.time() - start_time) / 60  # 总时长(分钟)
        effective_duration = (time.time() - start_time - self.total_follow_up_time) / 60  # 有效时长(分钟)，排除追评时间

        # 返回结果
        result = {
            "status": "success",
            "total_duration": round(total_duration, 2),
            "effective_duration": round(effective_duration, 2),
            "follow_up_time": round(self.total_follow_up_time, 1),
            "total_posts": total_posts,
            "interactions": interactions
        }

        # 打印任务总结
        logger.info("========== 浏览互动任务完成 ==========")
        if self.total_follow_up_time > 0:
            logger.info(f"✅ 总用时: {total_duration:.2f}分钟（含追评 {self.total_follow_up_time:.1f} 秒），有效互动时长: {effective_duration:.2f}分钟")
        else:
            logger.info(f"✅ 总用时: {total_duration:.2f}分钟")
        logger.info(f"✅ 浏览笔记: {total_posts}个")
        logger.info(f"✅ 互动统计: 点赞 {interactions['like']}次, 收藏 {interactions['collect']}次, " +
                   f"评论 {interactions['comment']}次, 关注 {interactions['follow']}次")
        
        # 如果auto_close_app为True，则在任务完成后关闭应用
        if auto_close_app:
            self.close_app()
        
        return result
    
    def execute(self,
              task_type: str = "browse",
              channel_type: str = "home",  # 新增频道类型参数
              duration_minutes: float = 30.0,
              max_posts: int = 50,
              like_probability: float = 0.7,
              collect_probability: float = 0.3,
              comment_probability: float = 0.2,
              follow_probability: float = 0.1,
              min_operation_interval: float = 2.0,  # 新增：最小操作间隔
              max_operation_interval: float = 5.0,  # 新增：最大操作间隔
              enable_comment: bool = False,
              comments: Optional[List[str]] = None,
              comments_file_path: Optional[str] = None,  # 评论文件路径参数
              comment_mode: str = "text",  # 评论类型参数
              sticker_templates: Optional[list] = None,  # 支持外部传入表情包模板
              sticker_id: Optional[int] = None,  # 支持外部指定表情包id
              sticker_templates_path: Optional[str] = None,  # 新增：表情包模板路径
              auto_close_app: bool = True,  # 是否自动关闭APP
              enable_follow_up: bool = False,  # 是否启用追评功能
              follow_up_templates: Optional[List[str]] = None,  # 追评文本模板
              follow_up_type: str = "sticker",  # 追评类型 ("text" 或 "sticker")
              **kwargs) -> Dict[str, Any]:
        """
        执行养号任务

        Args:
            task_type: 任务类型，可选值: browse
            channel_type: 频道类型 (home/shoes/beauty等)
            duration_minutes: 任务时长(分钟)
            max_posts: 最大浏览笔记数
            like_probability: 点赞概率
            collect_probability: 收藏概率
            comment_probability: 评论概率
            follow_probability: 关注概率
            min_operation_interval: 最小操作间隔(秒)
            max_operation_interval: 最大操作间隔(秒)
            enable_comment: 是否启用评论功能
            comments: 评论内容列表，如果提供则覆盖默认评论模板
            comments_file_path: 评论文件路径，如果提供则从文件读取评论
            comment_mode: "text" | "sticker" | "auto"
            sticker_templates: 表情包模板
            sticker_id: 指定表情包id
            sticker_templates_path: 表情包模板路径
            auto_close_app: 是否自动关闭APP
            enable_follow_up: 是否启用追评功能
            follow_up_templates: 追评文本模板列表
            **kwargs: 其他参数
            
        Returns:
            任务执行结果
        """
        # 重要：重置控制器的终止状态，确保新任务能正常开始
        self.is_terminated = False

        # 重置APP重启计数器
        self.restart_count = 0
        
        # 如果设备被标记为已终止，尝试重置状态
        if self.device and hasattr(self.device, 'is_terminated'):
            setattr(self.device, 'is_terminated', False)
            logger.info("重置设备终止状态，确保任务可以正常开始")
        
        # 如果设备连接断开，尝试重新连接
        if (self.device and
            hasattr(self.device, 'connected') and
            not self.device.connected and
            hasattr(self.device, 'connect')):
            logger.info("设备连接已断开，尝试重新连接")
            self.device.connect()
            # 等待短暂时间以确保连接稳定
            time.sleep(1)
            

        # 首先检查是否请求停止
        if self.is_terminated:
            logger.info("控制器已经被标记为终止状态，不执行养号任务")
            return {"status": "terminated", "message": "任务已被终止"}
            
        if self.check_stop_requested():
            logger.info("检测到停止请求，不执行养号任务")
            return {"status": "stopped", "message": "检测到停止请求"}
        
        try:
            # 记录任务开始和参数
            logger.info("========== 养号任务开始执行 ==========")
            logger.info(f"📝 任务类型: {task_type}")
            logger.info(f"📝 目标频道: {channel_type}")  # 新增频道类型日志
            logger.info(f"📝 计划时长: {duration_minutes}分钟")
            logger.info(f"📝 最大浏览数: {max_posts}个笔记")
            logger.info(f"📝 互动概率: 点赞 {like_probability*100:.0f}%, 收藏 {collect_probability*100:.0f}%, " +
                       f"评论 {comment_probability*100:.0f}%, 关注 {follow_probability*100:.0f}%")
            logger.info(f"📝 是否启用评论: {'是' if enable_comment else '否'}")

            # 存储频道类型供后续使用
            self._target_channel = channel_type
            
            # 处理评论文件路径（如果提供）
            if comments_file_path and os.path.exists(comments_file_path):
                try:
                    file_ext = os.path.splitext(comments_file_path)[1].lower()
                    file_comments = []
                    
                    # 只处理Excel文件
                    if file_ext in ['.xlsx', '.xls']:
                        try:
                            import pandas as pd
                            # 读取Excel文件的A列数据
                            logger.info(f"尝试从Excel文件读取评论: {comments_file_path}")
                            df = pd.read_excel(comments_file_path, engine='openpyxl' if file_ext == '.xlsx' else 'xlrd')
                            # 获取第一列的所有非空值，每个单元格作为一条完整评论
                            if not df.empty and len(df.columns) > 0:
                                col_values = df.iloc[:, 0].dropna().tolist()
                                file_comments = [str(val).strip() for val in col_values if str(val).strip()]
                                logger.info(f"成功从Excel文件 {comments_file_path} 的A列读取 {len(file_comments)} 条评论模板")
                                # 显示前三条评论内容预览
                                if file_comments:
                                    for i, comment in enumerate(file_comments[:3]):
                                        logger.info(f"评论模板 {i+1} 预览: {comment[:30]}...")
                        except ImportError:
                            logger.error("无法导入pandas库，请确保已安装pandas和openpyxl: pip install pandas openpyxl")
                        except Exception as e:
                            logger.error(f"读取Excel评论文件失败: {str(e)}")
                    else:
                        logger.warning(f"不支持的文件格式: {file_ext}，请使用Excel文件(.xlsx或.xls)")
                    
                    if file_comments:
                        logger.info(f"成功从文件读取 {len(file_comments)} 条评论模板")
                        comments = file_comments
                    else:
                        logger.warning(f"评论文件内容为空或格式不支持: {comments_file_path}")
                except Exception as e:
                    logger.error(f"读取评论文件失败: {str(e)}")
            
            # 更新评论模板 - 添加随机洗牌处理，确保每个容器获得随机顺序的评论
            if comments is not None:
                if comments:
                    # 随机洗牌评论列表，使每个容器获得随机顺序的评论
                    shuffled_comments = comments.copy()
                    random.shuffle(shuffled_comments)
                    self.comment_templates = shuffled_comments
                    logger.info(f"✅ 使用随机洗牌后的评论模板，共 {len(comments)} 条")
                    # 每条评论可能是多行内容，显示前三条评论的前30个字符
                    for i, comment in enumerate(shuffled_comments[:3]):
                        logger.info(f"评论模板 {i+1} 预览: {comment[:30]}...")
                        # 检查是否包含换行符，如果包含则提示
                        if '\n' in comment:
                            logger.info(f"评论模板 {i+1} 包含换行符，但将作为一个整体使用")
                else:
                    logger.warning("⚠️ 传入的评论模板列表为空，评论功能可能无法正常工作")
                    # 保留空列表
                    self.comment_templates = []
            
            # 检查评论功能是否可用
            if comment_mode == "text":
                if not self.comment_templates:
                    logger.warning("⚠️ 启用了文本评论但未提供评论内容，评论功能将不可用")
                    enable_comment = False
            elif comment_mode == "sticker":
                if not self.sticker_templates:
                    logger.warning("⚠️ 启用了表情包评论但未配置表情包模板，评论功能将不可用")
                    enable_comment = False
            elif comment_mode == "auto":
                # 修复随机模式逻辑 - 只要有一种评论方式可用就行
                has_text = bool(self.comment_templates)
                has_sticker = bool(self.sticker_templates)
                
                if has_text and has_sticker:
                    logger.info("✅ 随机评论模式：文本评论和表情包评论均可用")
                elif has_text:
                    logger.info("✅ 随机评论模式：仅文本评论可用，将固定使用文本评论")
                elif has_sticker:
                    logger.info("✅ 随机评论模式：仅表情包可用，将固定使用表情包评论")
                else:
                    logger.warning("⚠️ 启用了随机评论但未提供评论内容和表情包模板，评论功能将不可用")
                    enable_comment = False
            
            # 支持外部传入sticker_templates
            if sticker_templates is not None:
                self.sticker_templates = sticker_templates
            logger.info(f"[EXECUTE] 当前sticker_templates数量: {len(self.sticker_templates)}")

            # 调试：检查传入的追评参数
            logger.info(f"🔍 AccountNurturingController.execute() 接收到的追评参数: enable_follow_up={enable_follow_up}, follow_up_type={follow_up_type}, follow_up_templates数量={len(follow_up_templates) if follow_up_templates else 0}")

            # 设置追评配置（必须在执行任务前设置）
            logger.info(f"🔍 准备设置追评配置: enable_follow_up={enable_follow_up}, follow_up_type={follow_up_type}, follow_up_templates类型={type(follow_up_templates)}, 数量={len(follow_up_templates) if follow_up_templates else 0}")
            self.set_follow_up_config(enable_follow_up, follow_up_templates, follow_up_type)
            if enable_follow_up:
                logger.info(f"✅ 追评功能已启用，追评类型: {follow_up_type}, 追评模板数量: {len(self.follow_up_templates)}")
            else:
                logger.info("追评功能未启用")

            # 打开应用
            logger.info("📝 准备打开小红书应用")
            if not self.open_app():
                logger.error("❌ 打开应用失败，任务终止")
                return {"status": "error", "message": "打开应用失败"}

            logger.info("✅ 应用启动成功，开始执行任务")

            task_start_time = time.time()
            result = None
            
            # 执行浏览任务
            if task_type == "browse" or task_type == "search":
                # 所有任务类型都执行普通浏览任务
                if task_type == "search":
                    logger.info("搜索功能已移除，执行普通浏览任务")
                else:
                    logger.info("📝 执行普通浏览任务")
                
                result = self.browse_and_interact(
                    channel_type=channel_type,  # 新增频道类型参数
                    duration_minutes=duration_minutes,
                    max_posts=max_posts,
                    like_probability=like_probability,
                    collect_probability=collect_probability,
                    comment_probability=comment_probability,
                    follow_probability=follow_probability,
                    min_operation_interval=min_operation_interval,  # 新增操作间隔参数
                    max_operation_interval=max_operation_interval,  # 新增操作间隔参数
                    enable_comment=enable_comment,
                    comment_mode=comment_mode,
                    sticker_id=sticker_id,
                    sticker_templates_path=sticker_templates_path,  # 新增表情包模板路径
                    auto_close_app=auto_close_app,
                    **kwargs
                )
            else:
                logger.error(f"❌ 未知任务类型: {task_type}")
                return {"status": "error", "message": f"未知任务类型: {task_type}"}
            
            # 计算总时长
            task_duration = (time.time() - task_start_time) / 60  # 分钟
            
            # 任务完成总结
            logger.info("========== 养号任务执行完成 ==========")
            logger.info(f"✅ 任务类型: {task_type}")
            logger.info(f"✅ 实际用时: {task_duration:.2f}分钟")
            
            # 统计互动数据
            if result and result.get("status") == "success":
                if "interactions" in result:
                    interactions = result["interactions"]
                    logger.info(f"✅ 互动总结: 点赞 {interactions.get('like', 0)}次, " +
                               f"收藏 {interactions.get('collect', 0)}次, " +
                               f"评论 {interactions.get('comment', 0)}次, " +
                               f"关注 {interactions.get('follow', 0)}次")
                
                if "total_posts" in result:
                    logger.info(f"✅ 总浏览笔记: {result['total_posts']}个")
            

            
            # 如果auto_close_app为True，则在任务完成后关闭应用
            if auto_close_app:
                logger.info("任务完成，准备关闭小红书APP")
                self.close_app()
            
            return result
                
        except Exception as e:
            logger.error(f"❌ 执行养号任务异常: {str(e)}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e), "traceback": traceback.format_exc()}

    def verify_page_type(self, page_type: str) -> bool:
        """
        快速验证当前页面类型，只查找带有page_type属性的模板
        
        Args:
            page_type: 要验证的页面类型
            
        Returns:
            是否为指定类型的页面
        """
        # 不显示详细日志，只在找到匹配时记录
        if not self.element_manager:
            return False

        indicators = self.element_manager.find_page_indicators(page_type, min_matches=1, fast_mode=True)
        if indicators:
            logger.info(f"[快速验证] 确认当前页面为'{page_type}'")
            self.current_page = page_type
            return True
        return False

    def check_stop_requested(self) -> bool:
        """
        检查是否请求停止任务

        Returns:
            是否请求停止
        """
        # 首先检查终止标记
        if self.is_terminated:
            logger.info("控制器已被标记为终止状态，停止任务")
            return True

        # 检查设备是否已终止或断开连接
        if (self.device and
            hasattr(self.device, 'is_terminated') and
            self.device.is_terminated):
            logger.info("设备已被标记为终止状态，停止任务")
            self.is_terminated = True  # 同步终止状态
            return True

        # 检查设备连接是否已断开
        if (not self.device or
            not hasattr(self.device, 'connected') or
            not self.device.connected):
            logger.info("设备连接已断开，停止任务")
            self.is_terminated = True  # 标记为终止状态
            return True
            
        # 检查设备句柄是否已重置为0
        # 当设备句柄为0时，尝试重新连接而不是直接终止
        if (hasattr(self.device, 'myt') and self.device.myt and 
            hasattr(self.device.myt, '_handle') and self.device.myt._handle == 0):
            logger.info("设备句柄为0，尝试重新连接")
            # 尝试重新连接设备
            try:
                if self.device and hasattr(self.device, 'reconnect'):
                    if self.device.reconnect():
                        logger.info("设备重新连接成功，继续任务")
                        return False
                elif self.device and hasattr(self.device, 'connect'):
                    if self.device.connect():
                        logger.info("设备重新连接成功，继续任务")
                        return False
            except Exception as e:
                logger.error(f"尝试重新连接设备失败: {str(e)}")
            
            # 如果重连失败，标记为终止状态
            logger.info("设备重新连接失败，停止任务")
            self.is_terminated = True
            return True
            
        # 最后检查基类的停止事件
        try:
            return super().check_stop_requested()
        except AttributeError:
            # 如果基类没有check_stop_requested方法，返回False
            return False

    # 在类定义中添加一个新方法
    def detail_page_browse_with_scrolling(self, duration: float, is_post_interaction: bool = False) -> bool:
        """
        在详情页内带滑动的浏览，更自然地模拟用户行为
        
        Args:
            duration: 计划浏览时间
            is_post_interaction: 是否是互动后的浏览
        
        Returns:
            bool: 是否需要中断任务
        """
        if duration <= 0:
            return False
            
        phase_desc = "互动后" if is_post_interaction else "阅读期间"
        logger.info(f"📝 {phase_desc}滑动浏览 {duration:.1f} 秒")
        
        # 计算滑动次数 - 大约每1-2秒滑动一次
        scroll_interval = random.uniform(1, 2)
        scroll_count = max(1, int(duration / scroll_interval))
        
        start_time = time.time()
        elapsed_time = 0
        
        for i in range(scroll_count):
            # 检查是否达到计划浏览时间
            elapsed_time = time.time() - start_time
            if elapsed_time >= duration:
                break
                
            # 每次滑动前稍微等待 (但第一次滑动前等待较短)
            wait_time = random.uniform(0.5, 1.5) if i == 0 else random.uniform(1, 3)
            if self.random_wait(wait_time, wait_time * 1.2):
                return True
                
            # 随机决定滑动方向，但主要是向下滑动
            direction = "下" if random.random() < 0.8 else "上"
            
            # 在详情页内垂直滑动
            try:
                # 随机选择滑动起点的x坐标
                width = self.DEFAULT_SCREEN_WIDTH
                height = self.DEFAULT_SCREEN_HEIGHT
                
                # 随机生成x坐标，范围在屏幕宽度的20%-80%之间
                start_x = random.randint(int(width * 0.2), int(width * 0.8))
                # 保持终点x坐标与起点相同，保证垂直滑动
                end_x = start_x
                
                if direction == "下":  # 从下向上滑
                    start_y = int(height * 0.7)
                    end_y = int(height * 0.3)
                else:  # 从上向下滑
                    start_y = int(height * 0.3)
                    end_y = int(height * 0.7)
                    
                duration_ms = random.randint(500, 1000)
                
                # 先尝试贝塞尔曲线滑动
                success = self.swipe_with_bezier(start_x, start_y, end_x, end_y, duration_ms/1000.0)
                if not success:
                    success = self.swipe(start_x, start_y, end_x, end_y, duration_ms)
                    
                logger.info(f"✅ 详情页{direction}滑动成功: {i+1}/{scroll_count}")
                
                # 检查页面状态，确保仍在详情页
                if i % 2 == 1:  # 每隔几次滑动检查一下
                    logger.info("进行页面稳定性检查...")
                    time.sleep(1)
                    if not self.verify_page_type("detail"):
                        logger.warning("⚠️ 检测到页面已离开详情页")
                        return False
            except Exception as e:
                logger.warning(f"⚠️ 详情页滑动异常: {str(e)}")
        
        # 完成所有滑动后，检查是否还有剩余时间
        remaining_time = duration - (time.time() - start_time)
        if remaining_time > 0:
            logger.info(f"📝 剩余时间静置浏览 {remaining_time:.1f} 秒")
            return self.random_wait(remaining_time * 0.7, remaining_time)
        
        return False

    def _handle_container_crash_with_split_restart(self) -> bool:
        """
        🔧 新增：使用分拆式重启处理容器崩溃

        Returns:
            是否恢复成功
        """
        logger.warning("🔄 开始容器崩溃分拆式重启恢复流程")

        try:
            # 检查是否有分拆式重启管理器
            if not hasattr(self, 'split_restart_manager') or not self.split_restart_manager:
                logger.error("❌ 分拆式重启管理器未初始化")
                return False

            # 1. 标记恢复开始
            self.crash_detector.start_recovery()

            # 2. 保存当前任务状态
            task_state = self._save_current_task_state()

            # 3. 获取设备ID
            device_id = f"{getattr(self.device, 'host', '127.0.0.1')}:{getattr(self.device, 'port', '5555')}"

            # 4. 执行分拆式重启恢复
            recovery_success = self.split_restart_manager.execute_crash_recovery(
                device_id, task_state)

            if recovery_success:
                # 5. 重新建立连接
                if self._reconnect_after_split_restart():
                    # 6. 恢复任务状态
                    self._restore_task_state(task_state)

                    # 7. 标记恢复成功
                    self.crash_detector.finish_recovery(True)

                    logger.info("✅ 分拆式重启恢复成功")
                    return True

            # 恢复失败
            self.crash_detector.finish_recovery(False)
            logger.error("❌ 分拆式重启恢复失败")
            return False

        except Exception as e:
            logger.error(f"分拆式重启恢复异常: {str(e)}")
            if hasattr(self, 'crash_detector'):
                self.crash_detector.finish_recovery(False)
            return False

    def _reconnect_after_split_restart(self) -> bool:
        """
        🔧 新增：分拆式重启后重新建立连接

        Returns:
            是否重连成功
        """
        logger.info("🔌 分拆式重启后重新建立连接")

        try:
            # 1. 断开现有连接
            if self.device and hasattr(self.device, 'disconnect'):
                self.device.disconnect()
                logger.info("已断开现有连接")

            # 2. 等待服务就绪
            logger.info("等待服务就绪...")
            time.sleep(10)

            # 3. 重新连接
            if self.device and hasattr(self.device, 'connect'):
                connect_success = self.device.connect()
                if connect_success:
                    logger.info("✅ 重新连接成功")
                    return True
                else:
                    logger.error("❌ 重新连接失败")
                    return False
            else:
                # 如果没有connect方法，尝试检查连接
                if self.device and hasattr(self.device, 'check_connection'):
                    connection_ok = self.device.check_connection()
                    if connection_ok:
                        logger.info("✅ 连接检查通过")
                        return True
                    else:
                        logger.error("❌ 连接检查失败")
                        return False
                else:
                    logger.warning("设备对象没有连接方法，假设连接正常")
                    return True

        except Exception as e:
            logger.error(f"重新连接异常: {str(e)}")
            return False

    def _save_current_task_state(self) -> dict:
        """
        🔧 新增：保存当前任务状态

        Returns:
            任务状态字典
        """
        state = {
            'timestamp': time.time(),
            'task_type': getattr(self, 'current_task_type', None),
            'target_channel': getattr(self, '_target_channel', 'home'),
            'current_page': getattr(self, 'current_page', None),
            'task_progress': {
                'total_posts': getattr(self, 'total_posts', 0),
                'max_posts': getattr(self, 'max_posts', 0),
                'start_time': getattr(self, 'task_start_time', None)
            },
            'crash_recovery_info': {
                'recovery_time': time.time(),
                'detection_stats': self.crash_detector.get_detection_stats() if hasattr(self, 'crash_detector') else {},
                'recovery_stats': self.split_restart_manager.get_recovery_stats() if (hasattr(self, 'split_restart_manager') and self.split_restart_manager) else {}
            }
        }

        # 保存搜索相关状态
        if hasattr(self, 'search_keywords'):
            state['search_keywords'] = self.search_keywords
        if hasattr(self, 'last_search_keyword'):
            state['last_search_keyword'] = self.last_search_keyword

        logger.info(f"已保存任务状态")
        return state

    def _restore_task_state(self, state: dict) -> bool:
        """
        🔧 新增：恢复任务状态

        Args:
            state: 任务状态字典

        Returns:
            是否恢复成功
        """
        try:
            # 恢复基本状态
            if 'target_channel' in state:
                self._target_channel = state['target_channel']
            if 'current_page' in state:
                self.current_page = state['current_page']

            # 恢复任务进度
            if 'task_progress' in state:
                progress = state['task_progress']
                if hasattr(self, 'total_posts'):
                    self.total_posts = progress.get('total_posts', 0)
                if hasattr(self, 'max_posts'):
                    self.max_posts = progress.get('max_posts', 0)
                if hasattr(self, 'task_start_time'):
                    self.task_start_time = progress.get('start_time')

            # 恢复搜索状态
            if 'search_keywords' in state:
                self.search_keywords = state['search_keywords']
            if 'last_search_keyword' in state:
                self.last_search_keyword = state['last_search_keyword']

            logger.info(f"任务状态恢复成功")
            return True

        except Exception as e:
            logger.error(f"任务状态恢复失败: {str(e)}")
            return False

    def close_app(self) -> bool:
        """
        关闭小红书应用
        
        Returns:
            bool: 是否成功关闭
        """
        try:
            logger.info("尝试关闭小红书应用")
            
            # 首先检测当前页面
            current_page = self.detect_current_page()
            
            # 如果在详情页，先返回搜索结果页
            if current_page == "detail":
                self.back()
                time.sleep(1)
                
            # 根据当前页面决定需要按几次back键
            current_page = self.detect_current_page()
            if current_page == "search":
                # 从搜索页需要按两次返回
                back_count = 2
            elif current_page == "profile":
                # 从个人页需要按一次返回
                back_count = 1
            elif current_page == "home":
                # 从首页直接使用应用切换器关闭
                back_count = 2
            else:
                # 其他页面按三次back键
                back_count = 3
                
            # 执行多次back操作
            logger.info(f"执行{back_count}次back键操作关闭应用")
            for _ in range(back_count):
                # 🔧 修复停止响应：在循环中检查停止标志
                if self.check_stop_requested():
                    return False
                self.back()
                # 🔧 修复停止响应：使用可中断等待替代time.sleep
                if self.interruptible_sleep(0.8):
                    return False
                
            logger.info("✅ 已尝试通过Back键退出应用")
            return True
                
        except Exception as e:
            logger.error(f"关闭应用异常: {str(e)}")
        return False

    # ==================== 追评功能相关方法 ====================

    def _perform_pre_comment_swipes(self) -> None:
        """表情评论前执行5次向下滑动，模拟自然浏览行为"""
        try:
            logger.info("📝 开始执行表情评论前的5次向下滑动")

            # 获取屏幕尺寸
            width = self.DEFAULT_SCREEN_WIDTH
            height = self.DEFAULT_SCREEN_HEIGHT

            # 执行5次向下滑动
            for i in range(5):
                # 检查是否请求停止
                if self.check_stop_requested():
                    logger.info("⚠️ 检测到停止请求，中断滑动操作")
                    break

                # 参考详情页浏览的贝塞尔曲线滑动逻辑
                # 向下滑动：从上向下滑
                start_y = int(height * 0.3)  # 从屏幕30%位置开始
                end_y = int(height * 0.7)    # 滑动到屏幕70%位置

                # 1. 不使用固定的屏幕中心点作为起点
                start_x = random.randint(int(width * 0.3), int(width * 0.7))

                # 2. 终点添加轻微随机偏移
                end_x = start_x + random.randint(-30, 30)

                # 3. 确保坐标在屏幕安全范围内
                end_x = max(50, min(width - 50, end_x))

                # 4. 添加持续时间随机性
                duration = random.randint(800, 1500)
                duration_sec = duration / 1000.0
                natural_duration = duration_sec * random.uniform(0.9, 1.1)

                # 5. 贝塞尔曲线的控制点随机性
                bezier_start_x = start_x + random.randint(-15, 15)
                bezier_end_x = end_x + random.randint(-15, 15)

                # 6. 确保坐标在屏幕范围内
                bezier_start_x = max(50, min(width - 50, bezier_start_x))
                bezier_end_x = max(50, min(width - 50, bezier_end_x))

                logger.debug(f"表情评论前向下滑动({i+1}/5): ({bezier_start_x}, {start_y}) -> ({bezier_end_x}, {end_y}), 时长: {natural_duration:.2f}秒")

                try:
                    # 先尝试贝塞尔曲线滑动
                    success = self.swipe_with_bezier(bezier_start_x, start_y, bezier_end_x, end_y, natural_duration)

                    if not success:
                        logger.warning("⚠️ 贝塞尔曲线滑动失败，尝试普通滑动")
                        success = self.swipe(start_x, start_y, end_x, end_y, duration)

                except Exception as e:
                    logger.warning(f"⚠️ 高级滑动异常: {str(e)}，尝试普通滑动")
                    try:
                        success = self.swipe(start_x, start_y, end_x, end_y, duration)
                    except Exception as e2:
                        logger.error(f"❌ 普通滑动也失败: {str(e2)}")
                        success = False

                if success:
                    logger.info(f"✅ 表情评论前成功向下滑动 ({i+1}/5)")
                else:
                    logger.warning(f"⚠️ 表情评论前向下滑动失败 ({i+1}/5)")

                # 每次滑动后短暂等待
                wait_time = random.uniform(0.8, 1.5)
                time.sleep(wait_time)

            logger.info("✅ 表情评论前的5次向下滑动完成")

        except Exception as e:
            logger.error(f"表情评论前滑动异常: {str(e)}")
            logger.error(traceback.format_exc())

    # ==================== 频道切换功能相关方法 ====================

    def _switch_to_channel(self, channel_type: str) -> bool:
        """
        切换到指定频道

        Args:
            channel_type: 频道类型 ("home", "shoes", "fashion")

        Returns:
            是否切换成功
        """
        if channel_type == "home":
            logger.info("目标频道为首页，无需切换")
            return True

        logger.info(f"🔄 开始切换到频道: {channel_type}")

        try:
            # 步骤1: 向下滑动1次，使频道标签可见
            logger.info("📝 向下滑动显示频道标签")
            if not self._swipe_down_to_show_channels():
                logger.warning("向下滑动失败")
                return False
            time.sleep(1)  # 等待页面稳定

            # 步骤2: 获取频道模板名称
            template_name = self._get_channel_template_name(channel_type)
            if not template_name:
                logger.error(f"未知频道类型: {channel_type}")
                return False

            # 步骤3: 查找频道标签
            logger.info(f"📝 查找频道标签: {template_name}")
            if not self.element_manager:
                logger.error("ElementManager未初始化")
                return False

            channel_element = self.element_manager.find_element(template_name)

            if not channel_element:
                logger.error(f"未找到频道标签: {template_name}")
                return False

            # 步骤4: 点击频道标签
            logger.info(f"📝 点击频道标签: {template_name}")
            if self.element_manager.click_element(template_name):
                logger.info(f"📝 频道标签点击完成，等待页面加载...")
                time.sleep(3)  # 等待页面加载完成

                # 步骤5: 验证频道切换是否成功
                logger.info(f"📝 验证频道切换结果...")
                if self._verify_channel_switch(channel_type):
                    logger.info(f"✅ 成功切换到频道: {channel_type}")
                    return True
                else:
                    logger.error(f"❌ 频道切换验证失败: {channel_type}")
                    return False
            else:
                logger.error(f"❌ 点击频道标签失败: {template_name}")
                return False

        except Exception as e:
            logger.error(f"❌ 频道切换异常: {str(e)}")
            return False

    def _execute_follow_up_comment(self) -> bool:
        """执行追评操作的主流程"""
        try:
            # 记录追评开始时间
            follow_up_start_time = time.time()

            logger.info("🔄 开始执行追评操作")

            # 步骤1-2: 等待-点击评论按钮（最简化流程）
            if not self._navigate_to_comment_section():
                logger.warning("导航到评论区失败，追评操作终止")
                return False

            # 步骤3: 验证进入评论区并验证reply_button_1
            time.sleep(3)
            if not self._verify_comment_section_and_reply_button():
                logger.warning("未能进入评论区或reply_button_1不存在")
                return False

            # 步骤4-8: 发送追评内容（文本或表情）
            follow_up_text = None
            if hasattr(self, 'follow_up_type') and self.follow_up_type == "text" and self.follow_up_templates:
                # 文本追评需要选择文本
                follow_up_text = random.choice(self.follow_up_templates)

            if not self._send_follow_up_content(follow_up_text):
                logger.warning("发送追评内容失败")
                return False

            # 计算追评耗时
            self.follow_up_duration = time.time() - follow_up_start_time
            self.total_follow_up_time += self.follow_up_duration

            logger.info(f"✅ 追评操作成功，耗时 {self.follow_up_duration:.1f} 秒（累积追评时间: {self.total_follow_up_time:.1f} 秒）")
            return True

        except Exception as e:
            logger.error(f"追评操作异常: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def _navigate_to_comment_section(self) -> bool:
        """导航到评论区（最简化流程：等待-点击评论按钮）"""
        try:
            logger.info("📝 开始导航到评论区")

            # 步骤1: 等待1.5秒
            logger.info("📝 等待1.5秒")
            time.sleep(1.5)

            # 步骤2: 点击评论按钮
            logger.info("📝 点击评论按钮")
            if not self.click_element("comment_button"):
                logger.warning("点击评论按钮失败")
                return False

            logger.info("✅ 成功导航到评论区")
            return True

        except Exception as e:
            logger.error(f"导航到评论区异常: {str(e)}")
            return False



    def _verify_comment_section_and_reply_button(self) -> bool:
        """验证是否滑动到评论区（通过检测reply_button_1图像）"""
        try:
            if not self.element_manager:
                logger.warning("⚠️ ElementManager未初始化")
                return False

            # 直接检测reply_button_1图像，存在即表示已滑动到评论区
            if self.element_manager.find_element("reply_button_1"):
                logger.info("✅ 检测到reply_button_1，已成功滑动到评论区")
                return True
            else:
                logger.warning("⚠️ 未检测到reply_button_1，可能未滑动到评论区")
                return False
        except Exception as e:
            logger.error(f"验证评论区异常: {str(e)}")
            return False

    def _send_follow_up_content(self, follow_up_text: Optional[str] = None) -> bool:
        """发送追评内容（文本或表情）"""
        try:
            # 4. 图像定位'reply_button_1'，选择Y坐标最小的(最上方)匹配定位
            # 强制使用最上方选择逻辑，不依赖优化定位器
            logger.info("🎯 开始查找最上方的reply_button_1")

            # 直接使用element_manager的find_topmost_element_by_template方法
            if self.element_manager and hasattr(self.element_manager, 'find_topmost_element_by_template'):
                topmost_reply_button = self.element_manager.find_topmost_element_by_template("reply_button_1")
                if topmost_reply_button:
                    # 点击最上方的reply_button_1
                    logger.info(f"✅ 找到最上方的reply_button_1: ({topmost_reply_button.get('x', 0)}, {topmost_reply_button.get('y', 0)})")
                    if hasattr(self.element_manager, 'click_coordinates'):
                        if self.element_manager.click_coordinates(topmost_reply_button, "最上方的reply_button_1", no_offset=True):
                            logger.info("✅ 成功点击最上方的reply_button_1")
                        else:
                            logger.warning("点击最上方reply_button_1失败，回退到标准方法")
                            if not self.click_element("reply_button_1"):
                                logger.warning("点击reply_button_1失败")
                                return False
                    else:
                        # 如果没有click_coordinates方法，使用设备直接点击
                        x = topmost_reply_button.get('x', 0)
                        y = topmost_reply_button.get('y', 0)

                        # 转换numpy类型为Python原生类型
                        if hasattr(x, 'item'):
                            x = x.item()
                        if hasattr(y, 'item'):
                            y = y.item()

                        if x > 0 and y > 0:
                            logger.info(f"使用设备直接点击最上方reply_button_1: ({x}, {y})")
                            # 优先使用click方法，回退到tap方法
                            click_success = False
                            if hasattr(self.device, 'click'):
                                click_success = self.device.click(x, y)
                                if click_success:
                                    logger.info("✅ 设备click方法点击最上方reply_button_1成功")
                            elif hasattr(self.device, 'tap'):
                                click_success = self.device.tap(x, y)
                                if click_success:
                                    logger.info("✅ 设备tap方法点击最上方reply_button_1成功")

                            if not click_success:
                                logger.warning("设备直接点击失败，回退到标准方法")
                                if not self.click_element("reply_button_1"):
                                    logger.warning("点击reply_button_1失败")
                                    return False
                        else:
                            logger.warning("无效坐标，回退到标准方法")
                            if not self.click_element("reply_button_1"):
                                logger.warning("点击reply_button_1失败")
                                return False
                else:
                    logger.warning("未找到最上方的reply_button_1，使用标准方法")
                    if not self.click_element("reply_button_1"):
                        logger.warning("点击reply_button_1失败")
                        return False
            else:
                # element_manager不支持find_topmost_element_by_template，使用标准方法
                logger.warning("ElementManager不支持find_topmost_element_by_template方法，使用标准方法")
                if not self.click_element("reply_button_1"):
                    logger.warning("点击reply_button_1失败")
                    return False
            time.sleep(2)  # 增加等待时间，确保页面稳定

            # 10. 图像定位'reply_button_2'，进行点击
            if not self.click_element("reply_button_2"):
                logger.warning("点击reply_button_2失败")
                return False
            time.sleep(2)  # 增加等待时间，确保输入框加载完成

            # 11. 根据追评类型选择不同的处理方式
            if hasattr(self, 'follow_up_type') and self.follow_up_type == "sticker":
                # 表情追评流程
                logger.info("🎭 执行表情追评流程")

                # 11a. 点击表情按钮（打开表情面板）
                if not self.click_element("sticker_panel_button"):
                    logger.warning("点击表情按钮失败")
                    return False
                time.sleep(1)

                # 11b. 点击表情包按钮（切换到表情包分组/标签页）
                if not self.click_element("sticker_tab_button"):
                    logger.warning("点击表情包按钮失败")
                    return False
                time.sleep(1.5)

                # 11c. 选择表情包
                if not self.sticker_templates:
                    logger.warning("表情包模板为空，无法进行表情追评")
                    return False

                import random
                sticker = random.choice(self.sticker_templates)
                sticker_element = sticker.get("element")
                if not self.click_element(sticker_element):
                    logger.warning(f"点击表情包{sticker_element}失败")
                    return False
                time.sleep(1)

                logger.info("✅ 表情追评内容选择成功")
            else:
                # 文本追评流程
                logger.info("📝 执行文本追评流程")
                if not follow_up_text:
                    logger.warning("文本追评但未提供追评文本")
                    return False

                # 11. 输入追评文本
                if not self.input_text(follow_up_text):
                    logger.warning("输入追评文本失败")
                    return False
                time.sleep(1)

                logger.info("✅ 文本追评内容输入成功")

            # 12. 点击发送按钮
            if not self.click_element("comment_send_button"):
                logger.warning("点击发送按钮失败")
                return False
            time.sleep(2)

            logger.info(f"✅ 追评{'表情' if hasattr(self, 'follow_up_type') and self.follow_up_type == 'sticker' else '文本'}发送成功")
            return True

        except Exception as e:
            logger.error(f"发送追评内容异常: {str(e)}")
            return False

    def set_follow_up_config(self, enable_follow_up: bool, follow_up_templates: Optional[List[str]] = None, follow_up_type: str = "sticker"):
        """设置追评配置

        Args:
            enable_follow_up: 是否启用追评功能
            follow_up_templates: 追评文本模板列表
            follow_up_type: 追评类型 ("text" 或 "sticker")
        """
        self.enable_follow_up = enable_follow_up
        self.follow_up_type = follow_up_type  # 存储追评类型
        if follow_up_templates is not None:
            self.follow_up_templates = follow_up_templates
        else:
            # 如果传入None，则清空追评模板
            self.follow_up_templates = []

        logger.info(f"追评配置已更新: 启用={enable_follow_up}, 类型={follow_up_type}, 模板数量={len(self.follow_up_templates)}")

    def reset_follow_up_time(self):
        """重置追评时间累积（用于新关键词开始时）"""
        self.total_follow_up_time = 0
        logger.debug("追评时间累积已重置")

    def _restart_app_and_recover_channel(self, target_channel: str) -> bool:
        """
        重启APP并恢复到目标频道

        Args:
            target_channel: 目标频道类型 (home/shoes/fashion)

        Returns:
            bool: 是否成功恢复
        """
        try:
            # 检查重启次数限制
            if self.restart_count >= self.max_restart_count:
                logger.error(f"❌ 已达到最大重启次数限制 {self.max_restart_count}，停止重启")
                return False

            self.restart_count += 1
            logger.info(f"🔄 第 {self.restart_count} 次重启APP并恢复到频道: {target_channel}（最多 {self.max_restart_count} 次）")

            # 步骤1: 关闭小红书APP
            logger.info("📝 正在关闭小红书APP...")
            if hasattr(self.device, 'stop_app'):
                stop_result = self.device.stop_app("com.xingin.xhs")
                logger.info(f"关闭APP结果: {stop_result}")
            else:
                logger.warning("设备不支持stop_app方法，尝试使用close_app")
                self.close_app()

            # 等待APP完全关闭
            time.sleep(3)

            # 步骤2: 重新打开小红书APP（使用完整的open_app方法）
            logger.info("📝 正在重新打开小红书APP...")
            if not self.open_app():
                logger.error("❌ APP重新打开失败")
                return False

            logger.info("✅ APP重新打开成功")

            # 步骤3: 验证APP状态（open_app方法已包含首页检查）
            current_page = self.detect_current_page()
            if not current_page.startswith("home"):
                logger.error(f"❌ 重启后APP状态异常，当前页面: {current_page}")
                return False

            # 步骤4: 重启恢复成功，频道切换将在主循环中处理
            # 注意：不在这里进行频道切换，避免与browse_and_interact中的逻辑重复
            # 频道切换将在主循环的第1644-1650行正常执行
            logger.info(f"✅ APP重启恢复成功，将在主循环中处理频道切换到: {target_channel}")
            return True

        except Exception as e:
            logger.error(f"❌ APP重启恢复异常: {str(e)}")
            return False