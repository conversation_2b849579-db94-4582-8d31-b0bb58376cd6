2025-07-29 14:43:23,279 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🚀 开始执行任务: 6ddb90f2-c084-41e5-b477-73fd91faf8d7, 类型: interaction
2025-07-29 14:43:23,279 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 📱 设备: **********:11050
2025-07-29 14:43:23,279 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ⚙️ 参数: {'interaction_type': 'comment', 'target_type': 'keyword', 'target': None, 'max_targets': 52, 'duration_minutes': 44.0, 'like_probability': 0.18, 'collect_probability': 0.12, 'comment_probability': 0.99, 'follow_probability': 0.0, 'min_view_time': 10.0, 'max_view_time': 17.0, 'stay_in_search': True, 'auto_close_app': False, 'search_keywords': [], 'comment_mode': 'sticker', 'config_json_path': 'configs/XHSTemplateStore', 'comments': [], 'enable_follow_up_comment': True, 'follow_up_comments': [], 'follow_up_type': 'sticker', 'device_ip': '**********', 'device_port': 11050, 'container_name': '61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1'}
2025-07-29 14:43:23,282 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🛡️ 容器信息已安全注册: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 14:43:23,283 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔒 容器安全信息已保存到任务参数
2025-07-29 14:43:23,283 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔧 创建RPC客户端配置...
2025-07-29 14:43:23,283 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 📋 连接配置: ConnectionConfig(retry_interval=5, max_retries=3, health_check_interval=30, connection_timeout=3)
2025-07-29 14:43:23,283 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔌 初始化增强RPC客户端...
2025-07-29 14:43:23,288 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 增强RPC客户端初始化完成
2025-07-29 14:43:23,288 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔗 开始连接设备（支持自动重试和容器重启）...
2025-07-29 14:43:23,288 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 📊 解析容器索引: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 -> 5
2025-07-29 14:43:23,310 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 设备连接成功
2025-07-29 14:43:23,310 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔍 连接成功，启动健康监控...
2025-07-29 14:43:23,317 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 健康监控启动成功并已注册
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 健康状态已发布到监控桥接器
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔍 验证设备连接...
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 获取到原始RPC客户端: <class 'xhs_matrix.core.enhanced_rpc_client.OptimizedMytRpc'>
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔍 测试设备响应...
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 📊 设备连接状态: True
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 设备支持openApp方法
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 设备响应测试完成
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🎛️ 使用连接时设置的工作模式（关闭无障碍）
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔧 准备控制器参数...
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🔧 已将健康监控器传递给控制器参数
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 📋 控制器参数: ['device', 'config_path', 'config_json_path', 'task_id', 'device_ip', 'device_port', 'container_id', 'logger_instance', 'health_monitor']
2025-07-29 14:43:23,320 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🏗️ 创建控制器实例: interaction
2025-07-29 14:43:23,370 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - ERROR - ❌ 创建控制器异常: interaction, 错误: InteractionController.__init__() got an unexpected keyword argument 'task_id'
2025-07-29 14:43:23,371 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - ERROR - 📋 异常堆栈: Traceback (most recent call last):
  File "d:\xhs_matrix_v2.0.0\xhs_matrix\domain\services\controller_adapter.py", line 287, in execute_task_in_process
    controller = self.create_controller(task_type, controller_params)
  File "d:\xhs_matrix_v2.0.0\xhs_matrix\domain\services\controller_adapter.py", line 75, in create_controller
    return InteractionController(**controller_params)
TypeError: InteractionController.__init__() got an unexpected keyword argument 'task_id'

2025-07-29 14:43:23,371 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - ERROR - 任务执行异常: ❌ 创建控制器异常: interaction, 错误: InteractionController.__init__() got an unexpected keyword argument 'task_id'
2025-07-29 14:43:23,371 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - 🛡️ 容器信息已安全注销
2025-07-29 14:43:28,384 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - ✅ 连接健康监控已停止并注销
2025-07-29 14:43:28,385 - 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1 - INFO - RPC连接已断开
