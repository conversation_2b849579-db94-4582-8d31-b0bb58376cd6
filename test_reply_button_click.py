#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：检测并点击最上方的'回复1'图像模板
用于验证reply_button_1的定位和点击逻辑
"""

import sys
import os
import time
import json
from typing import Optional, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from xhs_matrix.legacy.element_manager import ElementManager
from xhs_matrix.core.enhanced_rpc_client import OptimizedMytRpc as XHSDevice
from xhs_matrix.common.logger import logger


class ReplyButtonTester:
    """回复按钮测试器"""

    def __init__(self, device_ip: str = "**********", device_port: int = 11050):
        """初始化测试器

        Args:
            device_ip: 设备IP地址
            device_port: 设备端口
        """
        self.device_ip = device_ip
        self.device_port = device_port
        self.device = None
        self.element_manager = None
        
    def setup_connection(self) -> bool:
        """建立设备连接"""
        try:
            logger.info(f"🔗 正在连接设备 {self.device_ip}:{self.device_port}")

            # 创建XHSDevice实例
            self.device = XHSDevice()

            # 初始化连接
            if not self.device.init(self.device_ip, self.device_port, 10):
                logger.error("❌ 设备初始化失败")
                return False

            logger.info("✅ 设备连接成功")

            # 初始化元素管理器
            self.element_manager = ElementManager(device=self.device)

            if not self.element_manager:
                logger.error("❌ ElementManager初始化失败")
                return False

            # 加载配置文件
            config_count = self.element_manager.load_config("configs/nodes.json")
            logger.info(f"✅ ElementManager初始化成功，加载了 {config_count} 个元素配置")
            return True

        except Exception as e:
            logger.error(f"❌ 连接设备异常: {str(e)}")
            return False
    
    def test_find_topmost_reply_button(self) -> Optional[Dict[str, Any]]:
        """测试查找最上方的reply_button_1"""
        try:
            logger.info("🔍 开始测试查找最上方的reply_button_1")

            if not self.element_manager:
                logger.error("❌ ElementManager未初始化")
                return None

            # 检查是否支持find_topmost_element_by_template方法
            if not hasattr(self.element_manager, 'find_topmost_element_by_template'):
                logger.error("❌ ElementManager不支持find_topmost_element_by_template方法")
                # 尝试使用find_element方法
                logger.info("🎯 尝试使用find_element('reply_button_1')")
                reply_button = self.element_manager.find_element("reply_button_1")
                if reply_button:
                    logger.info(f"✅ 找到reply_button_1: {reply_button}")
                    return reply_button
                else:
                    logger.warning("⚠️ 未找到reply_button_1")
                    return None

            # 查找最上方的reply_button_1
            logger.info("🎯 调用find_topmost_element_by_template('reply_button_1')")
            topmost_reply_button = self.element_manager.find_topmost_element_by_template("reply_button_1")

            if topmost_reply_button:
                x = topmost_reply_button.get('x', 0)
                y = topmost_reply_button.get('y', 0)
                logger.info(f"✅ 找到最上方的reply_button_1: 坐标({x}, {y})")
                logger.info(f"📍 完整信息: {topmost_reply_button}")
                return topmost_reply_button
            else:
                logger.warning("⚠️ 未找到最上方的reply_button_1")
                return None

        except Exception as e:
            logger.error(f"❌ 查找最上方reply_button_1异常: {str(e)}")
            return None
    
    def test_click_topmost_reply_button(self, reply_button_info: Dict[str, Any]) -> bool:
        """测试点击最上方的reply_button_1
        
        Args:
            reply_button_info: 回复按钮信息
            
        Returns:
            bool: 是否点击成功
        """
        try:
            logger.info("🖱️ 开始测试点击最上方的reply_button_1")
            
            if not self.element_manager:
                logger.error("❌ ElementManager未初始化")
                return False
            
            # 方法1: 使用click_coordinates方法
            if hasattr(self.element_manager, 'click_coordinates'):
                logger.info("🎯 使用click_coordinates方法点击")
                if self.element_manager.click_coordinates(reply_button_info, "最上方的reply_button_1", no_offset=True):
                    logger.info("✅ click_coordinates方法点击成功")
                    return True
                else:
                    logger.warning("⚠️ click_coordinates方法点击失败")
            
            # 方法2: 使用设备直接点击
            x = reply_button_info.get('x', 0)
            y = reply_button_info.get('y', 0)

            # 转换numpy类型为Python原生类型
            if hasattr(x, 'item'):
                x = x.item()
            if hasattr(y, 'item'):
                y = y.item()

            if x > 0 and y > 0:
                logger.info(f"🎯 使用设备直接点击坐标: ({x}, {y})")
                if self.device and hasattr(self.device, 'click'):
                    if self.device.click(x, y):
                        logger.info("✅ 设备直接点击成功")
                        return True
                    else:
                        logger.warning("⚠️ 设备直接点击失败")
                elif self.device and hasattr(self.device, 'tap'):
                    if self.device.tap(x, y):
                        logger.info("✅ 设备tap点击成功")
                        return True
                    else:
                        logger.warning("⚠️ 设备tap点击失败")
                else:
                    logger.warning("⚠️ 设备不支持click或tap方法")
            else:
                logger.warning(f"⚠️ 无效坐标: ({x}, {y})")

            return False
            
        except Exception as e:
            logger.error(f"❌ 点击最上方reply_button_1异常: {str(e)}")
            return False
    
    def run_test(self) -> bool:
        """运行完整测试"""
        try:
            logger.info("🚀 开始reply_button_1定位和点击测试")
            logger.info("=" * 60)
            
            # 1. 建立连接
            if not self.setup_connection():
                logger.error("❌ 测试失败：无法建立设备连接")
                return False
            
            # 等待一下确保连接稳定
            time.sleep(2)
            
            # 2. 查找最上方的reply_button_1
            reply_button_info = self.test_find_topmost_reply_button()
            if not reply_button_info:
                logger.error("❌ 测试失败：未找到reply_button_1")
                return False
            
            # 3. 点击最上方的reply_button_1
            if not self.test_click_topmost_reply_button(reply_button_info):
                logger.error("❌ 测试失败：点击reply_button_1失败")
                return False
            
            logger.info("✅ 测试完成：成功找到并点击最上方的reply_button_1")
            logger.info("=" * 60)
            logger.info("🛑 测试脚本即将停止，请观察实际效果")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试运行异常: {str(e)}")
            return False
        finally:
            # 清理资源
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.device:
                logger.info("🧹 清理设备连接")
                if hasattr(self.device, 'disconnect'):
                    self.device.disconnect()
        except Exception as e:
            logger.warning(f"⚠️ 清理资源时出现异常: {str(e)}")


def main():
    """主函数"""
    print("Reply Button 1 定位和点击测试脚本")
    print("=" * 60)
    print("目标设备: **********:11050")
    print("测试内容: 查找并点击最上方的'回复1'图像模板")
    print("=" * 60)
    
    # 创建测试器
    tester = ReplyButtonTester(device_ip="**********", device_port=11050)
    
    # 运行测试
    success = tester.run_test()
    
    if success:
        print("\n✅ 测试执行完成，请观察设备上的实际效果")
        print("📝 请告知测试结果：")
        print("   1. 是否找到了reply_button_1？")
        print("   2. 点击的是否是最上方的回复按钮？")
        print("   3. 点击操作是否成功执行？")
    else:
        print("\n❌ 测试执行失败，请检查日志信息")
    
    print("\n🛑 测试脚本结束")


if __name__ == "__main__":
    main()
