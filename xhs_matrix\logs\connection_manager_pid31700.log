2025-07-29 02:59:31,414 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔧 创建增强RPC客户端: 10.0.0.127:11050
2025-07-29 02:59:31,414 - xhs_matrix.core.enhanced_rpc_client - INFO - 🏷️ 容器名称已设置: 61e7dbcc29cc9ba7bf6711ffcbb87fb9_5_A1
2025-07-29 02:59:31,414 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔒 安全上下文已设置
2025-07-29 02:59:31,414 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 所有安全参数完整
2025-07-29 02:59:31,414 - xhs_matrix.core.enhanced_rpc_client - INFO - 🚀 开始连接设备流程: 10.0.0.127:11050
2025-07-29 02:59:31,415 - xhs_matrix.core.enhanced_rpc_client - INFO - 📋 连接配置: 最大重试3次, 连接超时3秒, 重试间隔5秒
2025-07-29 02:59:31,415 - xhs_matrix.core.enhanced_rpc_client - INFO - 🔗 [第1/3次] 开始连接尝试: 10.0.0.127:11050
2025-07-29 02:59:31,415 - xhs_matrix.core.enhanced_rpc_client - INFO - SDK动态库路径: d:\xhs_matrix_v2.0.0\demo_py_x64\lib\libmytrpc.dll
2025-07-29 02:59:31,417 - xhs_matrix.core.enhanced_rpc_client - INFO - 检测到SDK版本: 10
2025-07-29 02:59:31,438 - xhs_matrix.core.enhanced_rpc_client - INFO - 工作模式设置成功: ACCESSIBILITY_OFF
2025-07-29 02:59:31,439 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ [第1次] 设备连接成功: 10.0.0.127:11050
2025-07-29 02:59:31,439 - xhs_matrix.core.enhanced_rpc_client - INFO - ⏱️ 连接耗时: 本次尝试0.02秒, 总耗时0.02秒
2025-07-29 02:59:31,444 - xhs_matrix.core.health_monitor_manager - INFO - 全局健康监控管理器初始化完成
2025-07-29 02:59:31,444 - xhs_matrix.core.health_monitoring - INFO - 创建连接健康监控器: bf4264f0-1f8c-46ef-84f9-328862554983 -> 10.0.0.127:11050[5]
2025-07-29 02:59:31,445 - xhs_matrix.core.health_monitoring - INFO - 健康监控线程启动: bf4264f0-1f8c-46ef-84f9-328862554983
2025-07-29 02:59:31,445 - xhs_matrix.core.health_monitoring - INFO - 健康监控启动成功: bf4264f0-1f8c-46ef-84f9-328862554983
2025-07-29 02:59:31,445 - xhs_matrix.core.health_monitor_manager - INFO - ✅ 注册健康监控器: 10.0.0.127:5 (任务: bf4264f0-1f8c-46ef-84f9-328862554983)
2025-07-29 02:59:31,445 - xhs_matrix.core.health_monitoring - INFO - 健康监控等待初始延迟 10 秒，确保连接稳定...
2025-07-29 02:59:31,445 - xhs_matrix.core.health_monitor_manager - INFO - 📊 当前活跃监控器总数: 1
2025-07-29 02:59:31,446 - xhs_matrix.core.health_monitor_bridge - INFO - 健康监控桥接器初始化完成: D:\xhs_matrix_v2.0.0\xhs_matrix\logs\health_bridge
2025-07-29 02:59:43,050 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (286, 990) -> (278, 459), 时长: 1.1257164777101474秒
2025-07-29 02:59:44,177 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (286, 990) 到 (278, 459), 持续 1.1257164777101474秒, 成功率: 100.0%
2025-07-29 02:59:45,682 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (266, 781) -> (278, 182), 时长: 1.4258860018732933秒
2025-07-29 02:59:47,110 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (266, 781) 到 (278, 182), 持续 1.4258860018732933秒, 成功率: 100.0%
2025-07-29 02:59:53,209 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 338) -> (144, 294), 时长: 1.0698394855630788秒
2025-07-29 02:59:54,280 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 338) 到 (144, 294), 持续 1.0698394855630788秒, 成功率: 100.0%
2025-07-29 02:59:56,791 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (360, 384) -> (360, 896), 时长: 0.654秒
2025-07-29 02:59:57,446 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (360, 384) 到 (360, 896), 持续 0.654秒, 成功率: 100.0%
2025-07-29 02:59:58,952 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (328, 896) -> (328, 384), 时长: 0.974秒
2025-07-29 02:59:59,928 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (328, 896) 到 (328, 384), 持续 0.974秒, 成功率: 100.0%
2025-07-29 03:00:02,790 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (201, 896) -> (201, 384), 时长: 0.961秒
2025-07-29 03:00:03,753 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (201, 896) 到 (201, 384), 持续 0.961秒, 成功率: 100.0%
2025-07-29 03:00:06,263 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (512, 384) -> (512, 896), 时长: 0.554秒
2025-07-29 03:00:06,819 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (512, 384) 到 (512, 896), 持续 0.554秒, 成功率: 100.0%
2025-07-29 03:00:08,427 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (408, 384) -> (412, 896), 时长: 0.9288084646958741秒
2025-07-29 03:00:09,356 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (408, 384) 到 (412, 896), 持续 0.9288084646958741秒, 成功率: 100.0%
2025-07-29 03:00:10,762 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (352, 384) -> (319, 896), 时长: 0.9047689088354852秒
2025-07-29 03:00:11,668 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (352, 384) 到 (319, 896), 持续 0.9047689088354852秒, 成功率: 100.0%
2025-07-29 03:00:12,563 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (231, 384) -> (218, 896), 时长: 1.25711642533286秒
2025-07-29 03:00:13,821 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (231, 384) 到 (218, 896), 持续 1.25711642533286秒, 成功率: 100.0%
2025-07-29 03:00:14,709 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (305, 384) -> (334, 896), 时长: 1.6241418685081648秒
2025-07-29 03:00:16,334 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (305, 384) 到 (334, 896), 持续 1.6241418685081648秒, 成功率: 100.0%
2025-07-29 03:00:17,684 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (421, 384) -> (410, 896), 时长: 1.1424129927455922秒
2025-07-29 03:00:18,828 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (421, 384) 到 (410, 896), 持续 1.1424129927455922秒, 成功率: 100.0%
2025-07-29 03:00:50,424 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (401, 896) -> (401, 384), 时长: 0.844秒
2025-07-29 03:00:51,269 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (401, 896) 到 (401, 384), 持续 0.844秒, 成功率: 100.0%
2025-07-29 03:00:54,180 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (348, 384) -> (348, 896), 时长: 0.847秒
2025-07-29 03:00:55,028 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (348, 384) 到 (348, 896), 持续 0.847秒, 成功率: 100.0%
2025-07-29 03:01:09,355 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (528, 783) -> (523, 236), 时长: 0.6599386815439389秒
2025-07-29 03:01:10,016 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (528, 783) 到 (523, 236), 持续 0.6599386815439389秒, 成功率: 100.0%
2025-07-29 03:01:11,524 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (506, 839) -> (521, 453), 时长: 1.1654198316183306秒
2025-07-29 03:01:12,690 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (506, 839) 到 (521, 453), 持续 1.1654198316183306秒, 成功率: 100.0%
2025-07-29 03:01:18,342 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 367) -> (144, 400), 时长: 0.9261495488982062秒
2025-07-29 03:01:19,269 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 367) 到 (144, 400), 持续 0.9261495488982062秒, 成功率: 100.0%
2025-07-29 03:01:21,398 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (576, 273) -> (144, 221), 时长: 1.1857239070482657秒
2025-07-29 03:01:22,585 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (576, 273) 到 (144, 221), 持续 1.1857239070482657秒, 成功率: 100.0%
2025-07-29 03:01:24,694 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (155, 896) -> (155, 384), 时长: 0.557秒
2025-07-29 03:01:25,254 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (155, 896) 到 (155, 384), 持续 0.557秒, 成功率: 100.0%
2025-07-29 03:01:28,267 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (342, 896) -> (342, 384), 时长: 0.656秒
2025-07-29 03:01:28,924 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (342, 896) 到 (342, 384), 持续 0.656秒, 成功率: 100.0%
2025-07-29 03:01:33,470 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (213, 896) -> (213, 384), 时长: 0.736秒
2025-07-29 03:01:34,207 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (213, 896) 到 (213, 384), 持续 0.736秒, 成功率: 100.0%
2025-07-29 03:01:34,654 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (224, 384) -> (221, 896), 时长: 0.8635647002662483秒
2025-07-29 03:01:35,519 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (224, 384) 到 (221, 896), 持续 0.8635647002662483秒, 成功率: 100.0%
2025-07-29 03:01:36,480 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (354, 384) -> (341, 896), 时长: 0.9328658903120369秒
2025-07-29 03:01:37,415 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (354, 384) 到 (341, 896), 持续 0.9328658903120369秒, 成功率: 100.0%
2025-07-29 03:01:38,666 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (505, 384) -> (462, 896), 时长: 0.7713948066655039秒
2025-07-29 03:01:39,439 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (505, 384) 到 (462, 896), 持续 0.7713948066655039秒, 成功率: 100.0%
2025-07-29 03:01:40,570 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (251, 384) -> (235, 896), 时长: 0.856554178201854秒
2025-07-29 03:01:41,428 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (251, 384) 到 (235, 896), 持续 0.856554178201854秒, 成功率: 100.0%
2025-07-29 03:01:42,500 - xhs_matrix.core.enhanced_rpc_client - INFO - 开始贝塞尔曲线滑动: (326, 384) -> (338, 896), 时长: 1.242647072794813秒
2025-07-29 03:01:43,744 - xhs_matrix.core.enhanced_rpc_client - INFO - ✅ 贝塞尔曲线滑动完成: 从 (326, 384) 到 (338, 896), 持续 1.242647072794813秒, 成功率: 100.0%
